'use client';

import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { type AxiosError } from 'axios';

import { useAppUsageContext } from '@/context';
import { useJune } from '@/hooks';
import * as api from '@/services/apis';
import { ControlledCheckbox, ControlledToggle, TextArea, TextInput, TextLabelGroup, TwButton } from '@/ui/components';
import { cn } from '@/ui/utils';
import { checkIfAppUsageError, toast } from '@/utils';

import { ActionSidebarDivider } from '../ActionSidebar';

const PROMPT_MAX_LENGTH = 256;

interface AlertFormProps {
  action: 'create' | 'edit';
  alert?: api.Alert;
  submitButtonText?: string;
  widgetId?: number;
  sourceWidgetInsightId?: number | null;
  defaultPrompt?: string | null;
  showDivider?: boolean;
  showCancelButton?: boolean;
  onClose: () => void;
}

type AlertFormValues = {
  prompt: string;
  emailSelected: boolean;
  maxConsecutiveTriggers: string | undefined;
  isPauseEnabled: boolean;
};

export default function AlertForm({
  action,
  alert,
  submitButtonText,
  widgetId,
  sourceWidgetInsightId,
  defaultPrompt,
  showDivider = true,
  showCancelButton = false,
  onClose,
}: AlertFormProps) {
  const { isMutationDisabled, isPromptingDisabled } = useAppUsageContext();

  const queryClient = useQueryClient();

  const { track } = useJune();

  function handleError(error: AxiosError, action: 'create' | 'edit') {
    const isAppUsageError = checkIfAppUsageError(error);
    if (isAppUsageError) {
      if (action === 'edit') setValue('prompt', alert?.prompt ?? '');
      toast.error((error.response?.data as api.ApiError).error.message);
    } else {
      toast.error('Please try changing the prompt and submitting again.');
    }
  }

  const { isPending: isCreateAlertPending, mutate: createAlert } = useMutation({
    mutationKey: ['createAlert'],
    mutationFn: api.createAlert,
    onSuccess: () => {
      if (widgetId) {
        queryClient.invalidateQueries({ queryKey: ['widgetInsights', widgetId] });
        toast.success('New alert created.');
        track.success('Create widget', 'Insights');
      } else {
        queryClient.invalidateQueries({ queryKey: ['monitoring'] });
        toast.success('Alert created successfully.');
      }
      onClose();
    },
    onError: (error: AxiosError) =>
      widgetId ? toast.error('Please try changing the prompt and submitting again.') : handleError(error, 'create'),
  });

  const { isPending: isUpdateAlertPending, mutateAsync: updateAlert } = useMutation({
    mutationFn: (alertBody: api.AlertEditBody) => api.updateAlert(alert!.id, alertBody),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['monitoring'],
      });
      toast.success('Alert updated successfully.');
      onClose();
    },
    onError: (error: AxiosError) => handleError(error, 'edit'),
  });

  const isPending = isCreateAlertPending || isUpdateAlertPending;

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors, isValid, isDirty },
    setValue,
    resetField,
  } = useForm<AlertFormValues>({
    mode: 'onChange',
    defaultValues: {
      prompt: alert?.prompt ?? defaultPrompt ?? '',
      emailSelected: alert?.externalDelivery?.includes(api.ExternalDelivery.EMAIL) ?? false,
      maxConsecutiveTriggers: alert?.maxConsecutiveTriggers ? alert.maxConsecutiveTriggers.toString() : undefined,
      isPauseEnabled: alert?.isPauseEnabled ?? false,
    },
  });

  const isPauseEnabled = watch('isPauseEnabled');

  const onSubmit = ({ prompt, emailSelected, maxConsecutiveTriggers, isPauseEnabled }: AlertFormValues) => {
    if (alert) {
      const data: api.AlertEditBody = {
        externalDelivery: emailSelected ? [api.ExternalDelivery.EMAIL] : [],
        maxConsecutiveTriggers: maxConsecutiveTriggers ? Number(maxConsecutiveTriggers) : 3,
        isPauseEnabled,
      };
      if (prompt !== alert?.prompt) data.prompt = prompt;
      updateAlert(data);
    } else {
      createAlert({
        prompt,
        externalDelivery: emailSelected ? [api.ExternalDelivery.EMAIL] : [],
        maxConsecutiveTriggers: maxConsecutiveTriggers ? Number(maxConsecutiveTriggers) : 3,
        isPauseEnabled,
        sourceWidgetInsightId: sourceWidgetInsightId ?? undefined,
      });
    }
  };

  return (
    <form className={cn('flex w-full flex-col', showDivider ? 'gap-10' : 'gap-6')} onSubmit={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-6">
        <TextArea
          {...register('prompt', {
            required: 'Prompt is required',
            minLength: { value: 10, message: 'Prompt is too short' },
            maxLength: { value: PROMPT_MAX_LENGTH, message: 'Prompt is too long' },
          })}
          label="Prompt"
          disabled={isPending || (action === 'edit' && isPromptingDisabled)}
          error={errors?.prompt?.message}
          name="prompt"
          id="prompt"
          limitLength={PROMPT_MAX_LENGTH}
          helperText="Description"
          placeholder="Start typing..."
          rows={7}
        />

        {action === 'edit' && (
          <TextLabelGroup label="Display Title">
            <p className="text-gray-800 body-m-regular">{alert?.title}</p>
          </TextLabelGroup>
        )}

        <ControlledCheckbox
          name="emailSelected"
          control={control}
          disabled={isPending}
          label="Email"
          topLabel="Delivery Method"
        />
      </div>

      {showDivider && <ActionSidebarDivider />}

      <div className="flex flex-col gap-6">
        <TextLabelGroup
          label="Notification Rule"
          icon="infoSmall"
          iconTooltipText="Set how many times this alert can notify you in a row before it stops and waits for the condition to clear."
        >
          <ControlledToggle
            name="isPauseEnabled"
            control={control}
            leftLabel="Limit alert repeats"
            disabled={isPending}
          />
        </TextLabelGroup>
        {isPauseEnabled && (
          <TextInput
            {...register('maxConsecutiveTriggers', {
              min: { value: 1, message: 'Please enter a whole number greater than 0.' },
              max: { value: 100, message: 'Please enter a whole number less than or equal to 100.' },
            })}
            label="Number of times to notify"
            placeholder="3 (default)"
            helperText="This alert will notify you this many times before it stops."
            clearText="Reset"
            includeTextBelow
            disabled={isPending}
            onClear={() => resetField('maxConsecutiveTriggers')}
            error={errors.maxConsecutiveTriggers?.message}
            onInput={(e: React.FormEvent<HTMLInputElement>) => {
              const target = e.target as HTMLInputElement;
              target.value = target.value.replace(/[^0-9]/g, '');
            }}
            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
              if (e.key === 'Enter') {
                e.preventDefault();
              }
            }}
          />
        )}
      </div>

      <div className="flex flex-col gap-4">
        <TwButton
          width="full"
          disabled={
            !isValid ||
            (action === 'edit' && (!isDirty || isMutationDisabled)) ||
            (action === 'create' && isPromptingDisabled)
          }
          loading={isPending}
        >
          {submitButtonText}
        </TwButton>
        {showCancelButton && (
          <TwButton
            variant="tertiary-outlined"
            width="full"
            className="text-gray-600"
            onClick={onClose}
            disabled={isPending}
          >
            Cancel
          </TwButton>
        )}
      </div>
    </form>
  );
}
