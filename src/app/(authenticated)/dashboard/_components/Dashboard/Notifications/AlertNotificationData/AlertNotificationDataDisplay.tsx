import { useRouter } from 'next/navigation';

import { useAuthContext } from '@/context';
import { Routes } from '@/routes';
import * as api from '@/services/apis';
import { Icon, TwButton } from '@/ui/components';
import { cn } from '@/ui/utils';
import { dateUtils } from '@/utils';
import { setHighlightedAlertId } from '@/utils/session-storage';

import { getView, renderData } from './helpers';

interface AlertNotificationDataDisplayProps {
  notification: api.AlertNotificationWithData;
  onClose: () => void;
}

export default function AlertNotificationDataDisplay({ notification, onClose }: AlertNotificationDataDisplayProps) {
  const { company } = useAuthContext();
  const view = getView(notification.data.length);

  const router = useRouter();

  const handleViewAlert = () => {
    if (notification.type === 'alert' && notification.referenceId) {
      setHighlightedAlertId(notification.referenceId.toString());
    }
    router.push(Routes.MONITORING);
  };
  // eslint-disable-next-line no-console
  console.log('notification.referenceId', notification.referenceId);
  // eslint-disable-next-line no-console
  console.log('notification.type', notification.type);

  return (
    <div
      className={cn(
        'flex flex-col size-full max-w-[1920px] gap-8',
        view === 'simple' && 'h-fit wlg:h-full',
        view === 'extendedWithTitle' && 'h-fit',
      )}
    >
      <div className="flex items-center justify-between gap-8">
        <div className="flex flex-col gap-2">
          <span className="text-gray-900 heading-s-bold">{notification.title}</span>

          <div className="flex items-center gap-2">
            <Icon icon="calendar" className="text-gray-500" cursor="default" />

            <span className="text-gray-500 body-l-regular">
              {dateUtils.formatDate(notification.createdAt, company?.dateFormat)}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-6">
          {notification.type === 'alert' && notification.referenceId && (
            <TwButton
              variant="tertiary-outlined"
              size="tiny"
              iconLeft="alarm"
              className="grow whitespace-nowrap !text-[12px] !text-gray-600"
              onClick={handleViewAlert}
            >
              View Alert Rule
            </TwButton>
          )}
          <TwButton iconLeft="close" variant="tertiary-outlined" size="rounded-s" onClick={onClose} />
        </div>
      </div>

      {view === null ? (
        <div className="flex grow items-center justify-center">
          <span className="heading-xl-semibold">No data found!</span>
        </div>
      ) : (
        <div
          className={cn(
            'flex gap-4',
            view === 'simple' && 'flex-col wlg:flex-row wlg:overflow-hidden',
            view === 'extended' && 'flex-col overflow-hidden',
            view === 'extendedWithTitle' && 'flex-col',
          )}
        >
          {notification.data.map((data, index) => renderData(data, view, index))}
        </div>
      )}
    </div>
  );
}
