'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

import { MarkdownRenderer } from '@/app/components';
import { useAuthContext } from '@/context';
import { Routes } from '@/routes';
import * as api from '@/services/apis';
import { Icon, Tooltip, TwButton } from '@/ui/components';
import { cn } from '@/ui/utils';
import { dateUtils } from '@/utils';
import { setHighlightedAlertId } from '@/utils/session-storage';

import { useUpdateNotificationReadStatus } from '../hooks';

import * as helpers from './helpers';

interface NotificationProps {
  notification: api.Notification;
  index: number;
  action: () => void;
}

export default function Notification({ notification, index, action }: NotificationProps) {
  const { company } = useAuthContext();
  const { title, createdAt, isViewed, type, id: notificationId, referenceId } = notification;

  const router = useRouter();

  const updateReadStatus = useUpdateNotificationReadStatus(true);

  const { typeName, backgroundColor } = helpers.getNotificationDetails(notification);
  const { priorityIcon, tooltipText } = helpers.getPriorityDetails(notification.priority);

  const handleViewAlert = () => {
    if (type === 'alert' && referenceId) {
      setHighlightedAlertId(referenceId.toString());
    }
    router.push(Routes.MONITORING);
  };

  return (
    <div
      id={`notification-${index}`}
      className="group relative flex h-[298px] w-[240px] min-w-[240px] flex-col rounded-lg bg-white p-2"
    >
      <div className="flex items-center gap-1.5 px-1 pb-2 text-turquoise-900 body-xs-bold">
        <Tooltip text={tooltipText} position="top">
          <div>
            <Icon icon={priorityIcon} width={8} height={8} />
          </div>
        </Tooltip>
        <span className="uppercase">{dateUtils.formatDate(createdAt, company?.dateFormat)}</span>
      </div>
      <div
        className={cn(
          'z-10 gap-4 flex h-full max-h-[260px] flex-col justify-between rounded-md p-2 transition-all duration-200 group-hover:max-h-[220px]',
          backgroundColor,
        )}
      >
        <div className="w-fit rounded bg-black/5 px-2 py-1 uppercase text-gray-800 body-xs-bold">{typeName}</div>
        <div className="body-xl-regular ">
          <MarkdownRenderer
            content={title}
            className="line-clamp-6 break-words prose-p:body-xl-regular prose-strong:body-xl-bold"
          />
        </div>
      </div>
      <div className="absolute bottom-0 left-0 flex w-full gap-1 p-2 opacity-0 transition-all duration-200 group-hover:opacity-100">
        <TwButton
          variant="tertiary-outlined"
          size="small"
          className="min-h-8 grow whitespace-nowrap py-[5px] !text-[12px] leading-5"
          onClick={action}
        >
          Find out more
        </TwButton>
        <TwButton
          variant="tertiary-outlined"
          size="small"
          className="min-h-8 min-w-0 p-[7px] !text-[12px] leading-5"
          onClick={() => updateReadStatus({ read: !isViewed, type, id: notificationId })}
        >
          <Icon icon={isViewed ? 'notificationUnread' : 'check'} className="text-black" size="sm" />
        </TwButton>
        {type === 'alert' && referenceId && (
          <TwButton
            variant="tertiary-outlined"
            size="small"
            className="min-h-8 min-w-0 p-[7px] !text-[12px] leading-5"
            onClick={handleViewAlert}
          >
            <Icon icon="alarm" className="text-black" size="sm" />
          </TwButton>
        )}
      </div>
    </div>
  );
}
