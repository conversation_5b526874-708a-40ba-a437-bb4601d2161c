import { Chart, TooltipItem, TooltipModel } from 'chart.js';

import { Currency, DecimalPoint } from '@/types';
import { formatCurrency } from '@/utils/format-currency';

import * as forecastingHelpers from '../../../../helpers';

const TOOLTIP_MAX_WIDTH = 300;

const getTooltipItemBackground = (label: string | undefined) => {
  if (!label) return 'bg-black';

  switch (label) {
    case 'Balance':
      return 'bg-forecasting-balance';
    case 'Expected Balance':
      return 'bg-forecasting-balance';
    case 'Income':
      return 'bg-gradient-to-b from-forecasting-income-start to-forecasting-income-end';
    case 'Expected Income':
      return 'border border-forecasting-forecasted-income-border-start bg-gradient-to-b from-forecasting-forecasted-income-start to-forecasting-forecasted-income-end';
    case 'Expense':
      return 'bg-gradient-to-b from-forecasting-expense-start to-forecasting-expense-end';
    case 'Expected Expense':
      return 'border border-forecasting-forecasted-expense-border-start bg-gradient-to-b from-forecasting-forecasted-expense-start to-forecasting-forecasted-expense-end';
    default:
      return 'bg-black';
  }
};

interface RenderTooltipProps {
  context: { chart: Chart; tooltip: TooltipModel<'bar'> };
  currency: Currency;
  periods: Array<string>;
  decimalPoint?: DecimalPoint;
}

export function renderTooltip({ context, currency, decimalPoint }: RenderTooltipProps) {
  // Tooltip Element
  let tooltipEl = document.getElementById('forecasting-tooltip');

  // Create element on first render
  if (!tooltipEl) {
    tooltipEl = document.createElement('div');
    tooltipEl.id = 'forecasting-tooltip';
    tooltipEl.innerHTML = `<div id="tooltip-container" class="flex flex-col py-2 px-3 gap-2 rounded bg-white shadow-dark-l z-chartJSTooltip" />`;
    tooltipEl.style.maxWidth = `${TOOLTIP_MAX_WIDTH}px`;
    document.body.appendChild(tooltipEl);
    return;
  }

  const tooltipModel = context.tooltip;

  tooltipEl.className = 'absolute opacity-100 pointer-events-none flex -translate-x-1/2 -translate-y-1/2';

  // Hide if no tooltip
  if (tooltipModel.opacity === 0) {
    tooltipEl.className = 'absolute opacity-0';
    return;
  }

  const position = context.chart.canvas.getBoundingClientRect();
  const canvasHeight = position.bottom - position.top;
  let left = position.left + window.scrollX + tooltipModel.caretX;

  // Make sure it doesn't overflow to the right
  if (left > window.innerWidth - TOOLTIP_MAX_WIDTH / 2) {
    left -= TOOLTIP_MAX_WIDTH / 2 - (window.innerWidth - left);
  }

  tooltipEl.style.left = `${left}px`;
  tooltipEl.style.top = `${canvasHeight}px`;

  // Set caret Position
  tooltipEl.classList.remove('above', 'below', 'no-transform');
  if (tooltipModel.yAlign) {
    tooltipEl.classList.add(tooltipModel.yAlign);
  } else {
    tooltipEl.classList.add('no-transform');
  }

  const tooltipContainer = tooltipEl.querySelector('#tooltip-container');

  const balanceDataPoint = tooltipModel.dataPoints.find((dataPoint) => dataPoint.dataset.label === 'Balance');
  const forecastedBalanceDataPoint = tooltipModel.dataPoints.find(
    (dataPoint) => dataPoint.dataset.label === 'Expected Balance',
  );
  const actualIncomeDataPoint = tooltipModel.dataPoints.find((dataPoint) => dataPoint.dataset.label === 'Income');
  const forecastedIncomeDataPoint = tooltipModel.dataPoints.find(
    (dataPoint) => dataPoint.dataset.label === 'Expected Income',
  );
  const actualExpenseDataPoint = tooltipModel.dataPoints.find((dataPoint) => dataPoint.dataset.label === 'Expense');
  const forecastedExpenseDataPoint = tooltipModel.dataPoints.find(
    (dataPoint) => dataPoint.dataset.label === 'Expected Expense',
  );

  const renderDataLine = (dataPoint?: TooltipItem<'bar'>) => {
    if (!dataPoint) return '';

    return `
      <div class="w-full flex items-center justify-between gap-4">
        <div class="flex gap-2 items-center">
          <div class="size-4 rounded ${getTooltipItemBackground(dataPoint.dataset.label)}"></div>
          <div class="body-s-regular text-nowrap text-gray-500">${dataPoint.dataset.label}</div>
        </div>
        <div class="body-s-bold text-gray-900 text-nowrap">
          ${formatCurrency(dataPoint.raw as number, currency, decimalPoint)}
        </div>
      </div>
    `;
  };

  const renderDataSection = (title: string, dataPoints: Array<TooltipItem<'bar'> | undefined>) => {
    const filteredDataPoints = dataPoints.filter((dataPoint) => dataPoint != undefined);
    if (filteredDataPoints.length === 0) return '';

    return `
      <div class="flex flex-col gap-1">
        <h3 class="body-compact-s text-gray-400 uppercase">${title}</h3>
        <div class="w-full flex flex-col gap-2 overflow-hidden">
          ${filteredDataPoints.map(renderDataLine).join('')}
        </div>
      </div>
    `;
  };

  const title = tooltipModel.title[0] ?? 'N/A';
  const currentTimePeriod = forecastingHelpers.formatPeriod(forecastingHelpers.getCurrentTimePeriod());

  if (title === currentTimePeriod) {
    tooltipContainer!.innerHTML = `
        <h2 id="tooltip-title" class="w-full body-s-bold text-center text-gray-900">${title}</h2>
        <div class="w-full flex flex-col gap-3 overflow-hidden">
            ${renderDataSection('Income', [actualIncomeDataPoint, forecastedIncomeDataPoint])}
            ${renderDataSection('Expenses', [actualExpenseDataPoint, forecastedExpenseDataPoint])}
            ${renderDataSection('Balance', [balanceDataPoint])}
        </div>
        `;
  } else {
    tooltipContainer!.innerHTML = `
        <h2 id="tooltip-title" class="w-full body-s-bold text-center text-gray-900">${title}</h2>
        <div class="w-full flex flex-col gap-2 overflow-hidden">
        ${renderDataLine(actualIncomeDataPoint?.raw != null ? actualIncomeDataPoint : forecastedIncomeDataPoint)}
        ${renderDataLine(actualExpenseDataPoint?.raw != null ? actualExpenseDataPoint : forecastedExpenseDataPoint)}
        ${renderDataLine(balanceDataPoint?.raw != null ? balanceDataPoint : forecastedBalanceDataPoint)}
        </div>
        `;
  }
}

export function hideTooltip() {
  const tooltipEl = document.getElementById('forecasting-tooltip');
  if (!tooltipEl) return;

  tooltipEl.className = 'absolute opacity-0';
}
