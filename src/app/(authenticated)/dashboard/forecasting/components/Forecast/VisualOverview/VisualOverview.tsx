import { useEffect, useMemo, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'framer-motion';

import { currencies } from '@/constants';
import { useAuthContext } from '@/context';
import * as api from '@/services/apis';
import { cn } from '@/ui/utils';

import { useForecastingContext } from '../../../forecasting-context';
import { ForecastDetailsTabs } from '../../../types';
import { useContentContext } from '../content-context';
import EditModeFloater from '../EditModeFloater';
import { useProcessedForecastData } from '../hooks';
import { Table } from '../Table';

import { ForecastChart } from './ForecastChart';
import { getSummaryItems } from './helpers';
import OptionsBar from './OptionsBar';
import SummaryItem from './SummaryItem';

interface VisualOverviewProps {
  forecastData: api.ForecastData;
}

export default function VisualOverview({ forecastData }: VisualOverviewProps) {
  const portalTargetRef = useRef<HTMLElement | undefined>();

  const { company } = useAuthContext();
  const { activeTab } = useForecastingContext();
  const { isEditMode, preparedEditChanges, showChart, visualOverviewExpandedRows, setVisualOverviewExpandedRows } =
    useContentContext();

  const { processedForecastData, accountsWithEditChanges } = useProcessedForecastData(
    forecastData,
    preparedEditChanges,
    true,
  );

  const { incomeItems, expenseItems, endOfYearBalanceItems } = getSummaryItems(processedForecastData);

  const {
    data: bankAccounts = [],
    isPending: isBankAccountsPending,
    isError: isBankAccountsError,
  } = useQuery({
    queryKey: ['bankAccounts'],
    queryFn: api.getBankAccounts,
  });

  const allAccountsBalance = useMemo(() => {
    if (!bankAccounts || bankAccounts.length === 0) return undefined;
    return bankAccounts.reduce((sum, bankAccount) => {
      if (bankAccount.currentConvertedBalance == null) return sum;
      return sum + bankAccount.currentConvertedBalance![company?.currency ?? currencies.EUR];
    }, 0);
  }, [bankAccounts, company?.currency]);

  const cashBalanceItems = [
    {
      label: 'All accounts',
      amount: allAccountsBalance,
      currency: company?.currency ?? currencies.EUR,
      color: '',
      isError: isBankAccountsError,
      isPending: isBankAccountsPending,
    },
  ];

  useEffect(() => {
    portalTargetRef.current = document.body;
  }, []);

  useEffect(() => {
    const chartScrollContainer = document.querySelector('[data-container="chart-scroll"]');
    const tableScrollContainer = document.querySelector('[data-container="table-scroll"]');

    if (!chartScrollContainer || !tableScrollContainer) return;

    chartScrollContainer.scrollLeft = tableScrollContainer.scrollLeft;

    const chartScrollListener = () => {
      tableScrollContainer.removeEventListener('scroll', tableScrollListener);
      tableScrollContainer.scrollLeft = chartScrollContainer.scrollLeft;
      tableScrollContainer.addEventListener('scroll', tableScrollListener);
    };
    const tableScrollListener = () => {
      chartScrollContainer.removeEventListener('scroll', chartScrollListener);
      chartScrollContainer.scrollLeft = tableScrollContainer.scrollLeft;
      chartScrollContainer.addEventListener('scroll', chartScrollListener);
    };

    chartScrollContainer.addEventListener('scroll', chartScrollListener);
    tableScrollContainer.addEventListener('scroll', tableScrollListener);

    return () => {
      chartScrollContainer?.removeEventListener('scroll', chartScrollListener);
      tableScrollContainer?.removeEventListener('scroll', tableScrollListener);
    };
  }, [processedForecastData, showChart, activeTab]);

  useEffect(() => {
    if (!processedForecastData || activeTab !== ForecastDetailsTabs.VISUAL_OVERVIEW) return;

    const updateChartSize = () => {
      const chartContainer: HTMLDivElement | null = document.querySelector('[data-container="chart"]');
      const tableContainer: HTMLDivElement | null = document.querySelector('[data-container="table"]');

      if (!chartContainer || !tableContainer) return;

      const tableContentWidth = tableContainer.offsetWidth - 350 + 'px';
      chartContainer.style.minWidth = tableContentWidth;
      chartContainer.style.width = tableContentWidth;
    };

    updateChartSize();

    window.addEventListener('resize', updateChartSize);

    return () => {
      window.removeEventListener('resize', updateChartSize);
    };
  }, [processedForecastData, showChart, activeTab]);

  return (
    <>
      <div className="flex max-h-full max-w-full flex-col gap-4 overflow-hidden rounded-lg border border-gray-200 bg-white p-6">
        <motion.div
          className="relative w-full"
          animate={{ height: showChart ? '445px' : '168px', minHeight: showChart ? '445px' : '168px' }}
          transition={{ duration: 0.2 }}
        >
          <AnimatePresence>
            {!showChart && (
              <motion.section
                key="hidden-chart"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="absolute left-0 top-0 flex size-full w-full flex-col gap-4"
              >
                <OptionsBar forecastId={forecastData.id} isTracked={forecastData.isTracked} />
                <div className="flex h-fit min-h-[104px] w-full gap-4">
                  <SummaryItem title="Cash Balance" items={cashBalanceItems} showColors={false} />
                  {incomeItems && <SummaryItem title="End of year Income" items={incomeItems} showColors={false} />}
                  {expenseItems && <SummaryItem title="End of year Expense" items={expenseItems} showColors={false} />}
                  <SummaryItem title="End of year Closing Balance" items={endOfYearBalanceItems} showColors={false} />
                </div>
              </motion.section>
            )}
          </AnimatePresence>
          <motion.div
            key="visible-chart"
            animate={{ opacity: showChart ? 1 : 0 }}
            transition={{ duration: 0.2 }}
            className={cn('absolute left-0 top-0 size-full overflow-hidden', { 'pointer-events-none': !showChart })}
          >
            <div className="flex h-fit gap-4">
              <section className="flex w-[280px] min-w-[280px] flex-col gap-4">
                <SummaryItem title="Cash Balance" items={cashBalanceItems} showColors={false} />
                {incomeItems && <SummaryItem title="End of year Income" items={incomeItems} showColors={true} />}
                {expenseItems && <SummaryItem title="End of year Expense" items={expenseItems} showColors={true} />}
                <SummaryItem title="End of year Closing Balance" items={endOfYearBalanceItems} showColors={true} />
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="size-4 rounded bg-purple-200/30" />
                    <span className="text-gray-500 body-m-regular">Current Month</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="size-4 rounded bg-purple-100/30" />
                    <span className="text-gray-500 body-m-regular">Forecasted</span>
                  </div>
                </div>
              </section>
              <section className="flex min-h-full grow flex-col gap-2 overflow-hidden">
                <OptionsBar forecastId={forecastData.id} isTracked={forecastData.isTracked} />
                <ForecastChart forecast={processedForecastData} />
              </section>
            </div>
          </motion.div>
        </motion.div>
        <Table
          forecast={processedForecastData}
          expandedRows={visualOverviewExpandedRows}
          toggleExpandedRow={(name) => {
            if (visualOverviewExpandedRows.includes(name)) {
              setVisualOverviewExpandedRows((prev) => prev.filter((rowName) => rowName !== name));
            } else setVisualOverviewExpandedRows((prev) => [...prev, name]);
          }}
          backgroundColor="bg-white"
        />
      </div>
      {isEditMode && <EditModeFloater accountsWithEditChanges={accountsWithEditChanges} />}
    </>
  );
}
