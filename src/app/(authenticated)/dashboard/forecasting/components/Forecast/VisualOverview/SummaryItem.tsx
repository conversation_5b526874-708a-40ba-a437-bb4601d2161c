import { motion } from 'framer-motion';

import { useAuthContext } from '@/context';
import { Currency } from '@/types';
import { Skeleton } from '@/ui/components';
import { formatCurrency } from '@/utils/format-currency';

interface SummaryItemProps {
  title: string;
  items: Array<{
    label: string;
    amount: number | undefined;
    currency: Currency;
    color: string;
    isError?: boolean;
    isPending?: boolean;
  }>;
  showColors: boolean;
}

export default function SummaryItem({ title, items, showColors }: SummaryItemProps) {
  const { company } = useAuthContext();

  if (items.every((item) => item.isError)) return null;

  return (
    <motion.div
      animate={{ height: showColors ? 'h-fit' : 'h-full' }}
      className="flex grow flex-col gap-3 rounded-lg bg-gray-100 px-4 py-3"
    >
      <span className="text-gray-800 body-m-bold">{title}</span>
      <div className="flex w-full flex-col gap-2">
        {items.map((item) => {
          if (item.isError) return null;
          return (
            <div key={item.label} className="flex w-full items-center justify-between">
              <div className="flex items-center gap-2">
                {showColors && <span className="size-4 rounded-sm" style={{ backgroundColor: item.color }} />}
                <span className="text-gray-500 body-m-regular">{item.label}</span>
              </div>
              {item.isPending && <Skeleton className="h-5 w-[75px]" />}
              {!item.isPending && (
                <span className="text-gray-900 body-m-bold">
                  {formatCurrency(item.amount, item.currency, company?.decimalPoint)}
                </span>
              )}
            </div>
          );
        })}
      </div>
    </motion.div>
  );
}
