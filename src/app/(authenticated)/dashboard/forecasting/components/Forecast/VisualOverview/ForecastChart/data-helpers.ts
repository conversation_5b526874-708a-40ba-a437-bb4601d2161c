import { ScriptableContext } from 'chart.js';

import { colors } from '@/ui/theme/colors';

import * as forecastingHelpers from '../../../../helpers';
import { ProcessedForecastData } from '../../../../types';

import * as helpers from './helpers';

const BAR_THICKNESS = 24;
const BORDER_WIDTH = 1.5;
const BAR_BORDER_RADIUS = 4;

export function getBarData(forecast: ProcessedForecastData) {
  const currentTimePeriodIndex = forecastingHelpers.getCurrentTimePeriodIndex(forecast.periods);

  return [
    {
      label: 'Income',
      stack: 'income',
      data: forecast.periods.map((period) => {
        if (!forecast.income) return null;
        return forecast.income.actualData[period]?.amount ?? null;
      }),
      backgroundColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-income-start'],
          colors['forecasting-income-end'],
        );
      },
      barThickness: BAR_THICKNESS,
      borderRadius: BAR_BORDER_RADIUS,
      borderColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-income-border-start'],
          colors['forecasting-income-border-end'],
        );
      },
      borderWidth: BORDER_WIDTH,
    },
    {
      label: 'Expected Income',
      stack: 'income',
      data: forecast.periods.map((period, index) => {
        if (!forecast.income) return null;
        if (forecast.income.actualData[period]?.amount != null && index != currentTimePeriodIndex) return null;
        return forecast.income.forecastedData[period]?.amount ?? null;
      }),
      backgroundColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-forecasted-income-start'],
          colors['forecasting-forecasted-income-end'],
        );
      },
      barThickness: BAR_THICKNESS,
      borderRadius: BAR_BORDER_RADIUS,
      borderColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-forecasted-income-border-start'],
          colors['forecasting-forecasted-income-border-end'],
        );
      },
      borderWidth: BORDER_WIDTH,
    },
    {
      label: 'Expense',
      stack: 'expense',
      data: forecast.periods.map((period) => {
        if (!forecast.expense) return null;
        const amount = forecast.expense.actualData[period]?.amount ?? null;
        return amount != null ? Math.abs(Number(amount)) : null;
      }),
      backgroundColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-expense-start'],
          colors['forecasting-expense-end'],
        );
      },
      barThickness: BAR_THICKNESS,
      borderRadius: BAR_BORDER_RADIUS,
      borderColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-expense-border-start'],
          colors['forecasting-expense-border-end'],
        );
      },
      borderWidth: BORDER_WIDTH,
    },
    {
      label: 'Expected Expense',
      stack: 'expense',
      data: forecast.periods.map((period, index) => {
        if (!forecast.expense) return null;

        if (forecast.expense.actualData[index]?.amount != null && index != currentTimePeriodIndex) return null;

        const forecastedAmount = forecast.expense.forecastedData[period]?.amount ?? null;
        return forecastedAmount != null ? Math.abs(Number(forecastedAmount)) : null;
      }),
      backgroundColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-forecasted-expense-start'],
          colors['forecasting-forecasted-expense-end'],
        );
      },
      barThickness: BAR_THICKNESS,
      borderRadius: BAR_BORDER_RADIUS,
      borderColor: (context: ScriptableContext<'bar'>) => {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return helpers.getGradient(
          ctx,
          chartArea,
          colors['forecasting-forecasted-expense-border-start'],
          colors['forecasting-forecasted-expense-border-end'],
        );
      },
      borderWidth: BORDER_WIDTH,
    },
  ];
}

export function getLineData(forecast: ProcessedForecastData, showData: boolean = true) {
  const currentTimePeriodIndex = forecastingHelpers.getCurrentTimePeriodIndex(forecast.periods);
  const preparedForecastedClosingBalances = forecast.periods.map((period, index) => {
    if (index < currentTimePeriodIndex) return null;
    return forecast.balancesAndNetChanges.forecastedClosingBalances[period] ?? null;
  });

  const preparedActualClosingBalances = forecast.periods.map((period, index) => {
    if (index > currentTimePeriodIndex) return null;
    return forecast.balancesAndNetChanges.actualClosingBalances[period] ?? null;
  });

  if (currentTimePeriodIndex > 0 && preparedActualClosingBalances[currentTimePeriodIndex - 1]) {
    preparedForecastedClosingBalances[currentTimePeriodIndex - 1] =
      preparedActualClosingBalances[currentTimePeriodIndex - 1];
  }

  return [
    {
      type: 'line',
      label: 'Balance',
      data: preparedActualClosingBalances,
      backgroundColor: colors['forecasting-balance'],
      borderColor: colors['forecasting-balance'],
      borderWidth: showData ? undefined : 0,
      pointRadius: showData ? undefined : 0,
      pointHoverRadius: showData ? undefined : 0,
    },
    {
      type: 'line',
      label: 'Expected Balance',
      data: preparedForecastedClosingBalances,
      backgroundColor: colors['forecasting-balance'],
      borderColor: colors['forecasting-balance'],
      borderDash: [10, 10],
      borderWidth: showData ? undefined : 0,
      pointRadius: showData ? undefined : 0,
      pointHoverRadius: showData ? undefined : 0,
    },
  ];
}
