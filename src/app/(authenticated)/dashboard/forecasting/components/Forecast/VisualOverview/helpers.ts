import { ProcessedForecastData } from '../../../types';

function isPeriodInYear(period: string, year: number) {
  const [periodYear] = period.split('-');

  return Number(periodYear) === year;
}

function getLastPeriodOfCurrentYear(year: number) {
  return `${Number(year)}-12`;
}

export function getSummaryItems(forecast: ProcessedForecastData) {
  const currentYear = new Date().getFullYear();

  let incomeItems;
  if (forecast.income) {
    const incomeTotal = Object.entries(forecast.income.actualData).reduce((acc, [period, dataPoint]) => {
      if (isPeriodInYear(period, currentYear)) return Number(acc) + Number(dataPoint.amount);
      return acc;
    }, 0) as number;

    const incomeForecasted = Object.entries(forecast.income.forecastedData).reduce((acc, [period, dataPoint]) => {
      if (isPeriodInYear(period, currentYear)) return Number(acc) + Number(dataPoint.amount);
      return acc;
    }, 0) as number;

    incomeItems = [
      {
        label: 'Received',
        amount: incomeTotal,
        currency: forecast.currency,
        color: '#2EB779',
      },
      {
        label: 'Expected',
        amount: incomeForecasted,
        currency: forecast.currency,
        color: '#D0FBE8',
      },
    ];
  }

  let expenseItems;
  if (forecast.expense) {
    const expenseTotal = Math.abs(
      Object.entries(forecast.expense.actualData).reduce((acc, [period, dataPoint]) => {
        if (isPeriodInYear(period, currentYear)) return Number(acc) + Number(dataPoint.amount);
        return acc;
      }, 0) as number,
    );
    const expenseForecasted = Math.abs(
      Object.entries(forecast.expense.forecastedData).reduce((acc, [period, dataPoint]) => {
        if (isPeriodInYear(period, currentYear)) return Number(acc) + Number(dataPoint.amount);
        return acc;
      }, 0) as number,
    );

    expenseItems = [
      { label: 'Paid', amount: expenseTotal, currency: forecast.currency, color: '#F32323' },
      { label: 'Expected', amount: expenseForecasted, currency: forecast.currency, color: '#FAD6D6' },
    ];
  }

  const lastPeriod = getLastPeriodOfCurrentYear(currentYear);
  const lastMonthBalance =
    forecast.balancesAndNetChanges.actualClosingBalances[lastPeriod] ??
    forecast.balancesAndNetChanges.forecastedClosingBalances[lastPeriod];
  const endOfYearBalanceItems = [
    { label: 'Total', amount: lastMonthBalance ?? NaN, currency: forecast.currency, color: '#185FE4' },
  ];

  return { incomeItems, expenseItems, endOfYearBalanceItems };
}
