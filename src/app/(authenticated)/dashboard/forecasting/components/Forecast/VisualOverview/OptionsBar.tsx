import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAppUsageContext } from '@/context';
import * as api from '@/services/apis';
import { Icon, TwButton } from '@/ui/components';

import { useContentContext } from '../content-context';

interface OptionsBarProps {
  forecastId: number;
  isTracked: boolean;
}

export default function OptionsBar({ forecastId, isTracked }: OptionsBarProps) {
  const { showChart, setShowChart } = useContentContext();

  const queryClient = useQueryClient();

  const { isMutationDisabled } = useAppUsageContext();

  const { mutate: updateTracked, isPending: isUpdatingTracked } = useMutation({
    mutationFn: () =>
      api.updateForecast(forecastId, {
        isTracked: !isTracked,
      }),
    onSuccess: () => {
      queryClient.setQueryData<api.ForecastResponse>(['forecast', forecastId], (oldForecast) => {
        if (!oldForecast || oldForecast.responseType !== api.ForecastResponseType.DATA) return undefined;

        return {
          ...oldForecast,
          data: {
            ...oldForecast.data,
            isTracked: !oldForecast.data.isTracked,
          },
        };
      });
    },
  });

  return (
    <div className="flex h-10 w-full items-center justify-end gap-2">
      <TwButton
        variant="tertiary-outlined"
        size="small"
        className="flex h-10 w-[132px] items-center px-3 py-2 text-gray-600 body-m-semibold"
        onClick={() => setShowChart(!showChart)}
        iconLeft={showChart ? 'eyeHide' : 'eyeShow'}
      >
        {showChart ? 'Hide Chart' : 'Show Chart'}
      </TwButton>
      <div className="flex items-center gap-2">
        <div>
          <TwButton
            variant={isTracked ? 'primary-filled' : 'tertiary-outlined'}
            size="small"
            className="h-10 min-w-0"
            onClick={() => updateTracked()}
            disabled={isUpdatingTracked || isMutationDisabled}
          >
            <Icon icon="bell" />
          </TwButton>
        </div>
      </div>
    </div>
  );
}
