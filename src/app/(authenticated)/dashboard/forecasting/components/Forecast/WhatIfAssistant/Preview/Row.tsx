import { Dispatch, SetStateAction, useState } from 'react';

import * as api from '@/services/apis';
import { Currency } from '@/types';
import { Icon } from '@/ui/components';
import { cn } from '@/ui/utils';

import * as forecastingHelpers from '../../../../helpers';
import { ForecastLevel } from '../../../../types';

import Cell from './Cell';

interface RowProps {
  data: ForecastLevel;
  periods: api.ForecastData['periods'];
  depth?: number;
  currency: Currency;
  isFirstRow?: boolean;
  collapsedRows: Array<string>;
  toggleCollapsedRow: (rowName: string) => void;
  hoveredColumnIndex: number | null;
  setHoveredColumnIndex: Dispatch<SetStateAction<number | null>>;
}

export default function Row({
  data,
  periods,
  currency,
  depth = 0,
  isFirstRow = false,
  collapsedRows,
  toggleCollapsedRow,
  hoveredColumnIndex,
  setHoveredColumnIndex,
}: RowProps) {
  const [isHovered, setIsHovered] = useState(false);

  const currentTimePeriodIndex = forecastingHelpers.getCurrentTimePeriodIndex(periods);

  const isExpandable = !!data.children && data.children.length > 0;

  const uniqueRowName = `${data.id}-${data.name}-${data.leafCategoryId}`;
  const isExpanded = !collapsedRows.includes(uniqueRowName);

  return (
    <div className={`rounded-lg bg-white`}>
      <div
        key={data.name}
        className={cn('flex h-[48px] items-center min-w-fit rounded-lg bg-white', {
          'h-[52px]': depth <= 0 && !isFirstRow,
        })}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="sticky left-0 z-10 h-full bg-white">
          <div
            className={cn(
              'text-gray-800 relative px-4 flex items-center justify-between min-w-[350px] max-w-[350px] w-[350px] border-x border-b border-gray-200 h-full',
              {
                'cursor-pointer': isExpandable,
                'body-s-bold border-t rounded-tl-lg': depth <= 0,
                'body-s-medium': depth > 0,
                'text-gray-800': depth <= 1,
                'text-gray-500': depth > 1,
                'rounded-bl-lg': depth <= 0 && !isExpanded,
                'bg-sand-200/30': isHovered,
              },
            )}
            onClick={() => toggleCollapsedRow(uniqueRowName)}
            style={{ paddingLeft: 16 + depth * 32 }}
          >
            {data.name}
            <div className="flex w-fit items-center gap-3">
              {isExpandable && (
                <Icon
                  icon="chevronDown"
                  className={cn('transition-transform text-gray-400', {
                    '-rotate-90': !isExpanded,
                  })}
                />
              )}
            </div>
          </div>
        </div>
        {periods.map((period, index) => {
          return (
            <Cell
              index={index}
              key={`${data.id}-${data.name}-${period}`}
              depth={depth}
              amount={data.forecastedData[period]?.amount}
              isRowExpanded={isExpanded}
              isLastCell={index === periods.length - 1}
              isInHoveredRow={isHovered}
              isInHoveredColumn={hoveredColumnIndex === index}
              isCurrentTimePeriod={index === currentTimePeriodIndex}
              currency={currency}
              setHoveredColumnIndex={setHoveredColumnIndex}
            />
          );
        })}
      </div>
      {isExpanded &&
        data.children &&
        data.children.map((row, index) => (
          <Row
            key={index}
            data={{ ...row }}
            periods={periods}
            depth={depth + 1}
            currency={currency}
            collapsedRows={collapsedRows}
            toggleCollapsedRow={toggleCollapsedRow}
            hoveredColumnIndex={hoveredColumnIndex}
            setHoveredColumnIndex={setHoveredColumnIndex}
          />
        ))}
    </div>
  );
}
