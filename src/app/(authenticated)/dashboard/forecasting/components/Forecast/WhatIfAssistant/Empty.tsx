import { Icon, TwButton } from '@/ui/components';

export default function Empty() {
  return (
    <div className="flex grow flex-col gap-3 overflow-hidden rounded-xl border border-gray-200 bg-white p-6 pt-8">
      <div className="flex grow flex-col gap-8 overflow-hidden">
        <div className="flex w-full items-center justify-between gap-3">
          <div className="flex flex-col gap-3">
            <h2 className="text-gray-800 heading-s-bold">Let&apos;s set up a new scenario!</h2>
            <p className="text-gray-700 body-l-regular">
              Get started by telling us your financial plan , we’ll break it into smart, editable sections
              automatically.
            </p>
          </div>
          <Icon icon="compassStars" size="xxl" />
        </div>
        <div className="flex flex-col gap-8 overflow-auto gray-scrollbar">
          <div className="flex flex-col gap-3">
            <div className="flex items-center">
              <span className="text-gray-400 body-s-bold">RECOMMENDED STEPS</span>
              <div className="h-px grow bg-gray-200" />
            </div>
            <div className="flex items-start gap-3">
              <span className="min-w-[23px] rounded bg-turquoise-200 px-1 text-center text-gray-800 body-s-bold">
                1
              </span>
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-1">
                  <span className="text-gray-800 body-m-semibold">Describe your plan</span>
                  <p className="text-gray-800 body-m-regular">
                    Tell us about any planned income or expense , you can list several items at once.
                    <br />
                    Please include detailed information for each item so we can generate the most accurate forecast.
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-gray-800 body-compact-m">TRY TO MENTION FOR EACH ITEM:</span>
                  <div className="flex flex-col gap-1">
                    <div className="flex items-end gap-1">
                      <Icon icon="arrowBold" className="text-turquoise-500" />
                      <span className="text-gray-800 body-m-regular">Category (e.g. marketing, salaries, funding)</span>
                    </div>
                    <div className="flex items-end gap-1">
                      <Icon icon="arrowBold" className="text-turquoise-500" />
                      <span className="text-gray-800 body-m-regular">Amount</span>
                    </div>
                    <div className="flex items-end gap-1">
                      <Icon icon="arrowBold" className="text-turquoise-500" />
                      <span className="text-gray-800 body-m-regular">Timeframe (when it starts, for how long)</span>
                    </div>
                    <div className="flex items-end gap-1">
                      <Icon icon="arrowBold" className="text-turquoise-500" />
                      <span className="text-gray-800 body-m-regular">Trend (one-time, monthly, etc.)</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-gray-800 body-compact-m">PROMPT EXMAPLE</span>
                  <p className="w-fit rounded-xl bg-gray-200 px-4 py-3 text-gray-800 body-m-regular">
                    I&apos;ll receive $50,000 in funding in July 2025. I plan to spend half on marketing and half on
                    salaries over the next 3 months.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <span className="min-w-[23px] rounded bg-turquoise-200 px-1 text-center text-gray-800 body-s-bold">2</span>
            <div className="flex flex-col gap-1">
              <span className="text-gray-800 body-m-semibold">Refine & Preview</span>
              <p className="text-gray-800 body-m-regular">
                After you submit a prompt, we&apos;ll create separate category-based chats and ask a few follow-up
                questions.
                <br />
                Each conversation comes with a preview table that you can refine directly within the chat.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <span className="min-w-[23px] rounded bg-turquoise-200 px-1 text-center text-gray-800 body-s-bold">3</span>
            <div className="flex flex-col gap-1">
              <span className="text-gray-800 body-m-semibold">Apply or Remove Changes</span>
              <p className="text-gray-800 body-m-regular">
                When you&apos;re done, you can choose to apply the table to your scenario,
                <br /> or remove everything and leave your current setup unchanged.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2 self-end">
        <button disabled={true} className="px-2 py-1 text-gray-600 body-m-medium disabled:opacity-35">
          Remove
        </button>
        <TwButton disabled={true} size="small">
          Apply Changes
        </TwButton>
      </div>
    </div>
  );
}
