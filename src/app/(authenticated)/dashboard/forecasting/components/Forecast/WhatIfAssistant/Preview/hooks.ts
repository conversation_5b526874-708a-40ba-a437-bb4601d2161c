import { useMemo } from 'react';
import groupBy from 'lodash/groupBy';

import { AccountForecastData, ExtendedEditChange } from '@/app/(authenticated)/dashboard/forecasting/types';
import { useLeafCategories } from '@/hooks';
import * as api from '@/services/apis';

export function useTransformPreviewItemsToEditChanges(previewItems: Array<api.PreviewItem>): Array<ExtendedEditChange> {
  const { leafCategoriesById } = useLeafCategories();
  const editChanges = useMemo(() => {
    return previewItems.flatMap((item) => {
      return Object.entries(item.periods)
        .map(([period, amount]) => {
          const type = leafCategoriesById[item.leaf_category_id]?.type;

          if (!type) return undefined;

          return {
            type,
            period,
            accountId: item.account_id,
            accountName: item.account_name,
            leafCategoryId: item.leaf_category_id,
            amount,
            isHighlighted: true,
            associatedChanges: [],
          };
        })
        .filter((editChange) => editChange !== undefined);
    });
  }, [leafCategoriesById, previewItems]);

  return editChanges;
}

export function useTransformEditChangesToAccountForecastData(
  editChanges: Array<api.EditChange>,
): Array<AccountForecastData> {
  const result = useMemo(() => {
    const groupedByAccountAndLeafCategory = groupBy(
      editChanges,
      (editChange) => `${editChange.accountId}-${editChange.accountName}-${editChange.leafCategoryId}`,
    );

    const accountsForecastData = Object.values(groupedByAccountAndLeafCategory).map((group) => {
      const firstItem = group[0];

      const forecastedData = group.reduce(
        (acc, editChange) => {
          acc[editChange.period] = { amount: editChange.amount };
          return acc;
        },
        {} as Record<string, { amount: number }>,
      );

      return {
        accountId: firstItem.accountId,
        accountName: firstItem.accountName,
        leafCategoryId: firstItem.leafCategoryId,
        forecastedData,
        actualData: {},
      } as AccountForecastData;
    });

    return accountsForecastData;
  }, [editChanges]);

  return result;
}
