'use client';
import { createContext, Dispatch, type PropsWithChildren, SetStateAction, useContext, useState } from 'react';

import { ChatMessage } from '@/ui/components';

import * as constants from './constants';

type WhatIfContext = {
  nodelessMessages: Array<ChatMessage>;
  setNodelessMessages: Dispatch<SetStateAction<Array<ChatMessage>>>;
  streamingInProgress: boolean;
  setStreamingInProgress: Dispatch<SetStateAction<boolean>>;
  chatLoadingMessage: string;
  setChatLoadingMessage: Dispatch<SetStateAction<string>>;
};

const WhatIfContext = createContext<WhatIfContext>({
  nodelessMessages: [],
  setNodelessMessages: () => {},
  streamingInProgress: false,
  setStreamingInProgress: () => {},
  chatLoadingMessage: constants.ChatLoadingMessages.default,
  setChatLoadingMessage: () => {},
});

export const WhatIfContextProvider = ({ children }: PropsWithChildren) => {
  const [nodelessMessages, setNodelessMessages] = useState<Array<ChatMessage>>([]);
  const [streamingInProgress, setStreamingInProgress] = useState(false);
  const [chatLoadingMessage, setChatLoadingMessage] = useState(constants.ChatLoadingMessages.default);

  return (
    <WhatIfContext.Provider
      value={{
        nodelessMessages,
        setNodelessMessages,
        streamingInProgress,
        setStreamingInProgress,
        chatLoadingMessage,
        setChatLoadingMessage,
      }}
    >
      {children}
    </WhatIfContext.Provider>
  );
};

export const useWhatIfContext = () => {
  const context = useContext(WhatIfContext);

  if (!context) {
    throw new Error('useWhatIfContext must be used within WhatIfContextProvider');
  }

  return context;
};
