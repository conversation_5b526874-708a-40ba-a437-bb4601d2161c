import { useAppUsageContext } from '@/context';
import { ChatInterface, ChatMessageType } from '@/ui/components';

import Empty from './Empty';
import { useSendMessage } from './hooks';
import { useWhatIfContext } from './what-if-context';

export default function DefaultView() {
  const { nodelessMessages, setNodelessMessages, streamingInProgress } = useWhatIfContext();

  const { isPromptingDisabled } = useAppUsageContext();

  const { sendMessage } = useSendMessage();

  return (
    <section className="flex size-full gap-4 rounded-xl">
      <ChatInterface
        disabled={isPromptingDisabled}
        messages={nodelessMessages}
        onSendMessage={async (message) => {
          if (isPromptingDisabled) return;
          setNodelessMessages((prev) => [...prev, { type: ChatMessageType.SENT, message }]);
          await sendMessage(message);
        }}
        loading={streamingInProgress}
        className="min-w-[325px] max-w-[325px] rounded-xl border border-gray-200 bg-white"
        receivedMessageOptions={{ backgroundColor: 'bg-gray-100' }}
      />
      <div className="flex grow flex-col gap-2 overflow-hidden">
        <Empty />
      </div>
    </section>
  );
}
