import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

import * as api from '@/services/apis';
import { ErrorSection, Skeleton } from '@/ui/components';

import { useForecastingContext } from '../../../forecasting-context';
import { useContentContext } from '../content-context';

import DefaultView from './DefaultView';
import NodeView from './NodeView';

interface WhatIfAssistantProps {
  forecastData: api.ForecastData;
}

export default function WhatIfAssistant({ forecastData }: WhatIfAssistantProps) {
  const { activeScenarioId } = useForecastingContext();
  const { selectedNodeId, setSelectedNodeId } = useContentContext();

  const {
    data: workflowNodes,
    isPending: isWorkflowNodesPending,
    isError: isWorkflowNodesError,
    refetch: refetchWorkflowNodes,
  } = useQuery({
    queryKey: ['workflow', 'nodes', activeScenarioId],
    queryFn: () => api.getWorkflowNodes(activeScenarioId!),
    enabled: !!activeScenarioId,
  });

  useEffect(() => {
    if (workflowNodes && workflowNodes.length > 0 && !selectedNodeId) {
      setSelectedNodeId(workflowNodes[0].id);
    }
  }, [selectedNodeId, setSelectedNodeId, workflowNodes]);

  if (isWorkflowNodesPending) {
    return (
      <section className="flex size-full gap-4">
        <Skeleton className="h-full w-[325px] min-w-[325px]" />
        <Skeleton className="flex grow rounded-xl" />
      </section>
    );
  }

  if (isWorkflowNodesError) {
    return (
      <section className="flex size-full flex-col gap-4">
        <div className="h-10 min-h-10 w-full" />
        <ErrorSection className="pb-0" resetFn={refetchWorkflowNodes} />
      </section>
    );
  }

  return (
    <>
      {selectedNodeId && <NodeView nodes={workflowNodes} forecastData={forecastData} />}
      {!selectedNodeId && <DefaultView />}
    </>
  );
}
