import { useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';

import { useAppUsageContext } from '@/context';
import { Icon, TextArea } from '@/ui/components';
import { cn } from '@/ui/utils';

import { useContentContext } from '../content-context';

import { useOnSendMessage } from './hooks';
import { useWhatIfContext } from './what-if-context';

export default function OptionsBar() {
  const { showExpandedPreview } = useContentContext();
  const { streamingInProgress } = useWhatIfContext();

  const { isPromptingDisabled } = useAppUsageContext();
  const { onSendMessage } = useOnSendMessage();

  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const isSendingMessagesDisabled = isPromptingDisabled || streamingInProgress;

  return (
    <AnimatePresence>
      {showExpandedPreview && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 40 }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.2 }}
          className="relative z-10 max-h-10"
        >
          <div className="absolute left-0 top-0 h-auto w-full p-1">
            <TextArea
              ref={textAreaRef}
              onKeyDown={(event) => {
                if (!textAreaRef.current || isSendingMessagesDisabled) return;
                if (event.key !== 'Enter' || event.shiftKey) return;

                onSendMessage(textAreaRef.current.value);
              }}
              disabled={isSendingMessagesDisabled}
              placeholder="Refine your prompt here..."
              rows={1}
              className="[&>div]:hidden [&>textarea]:h-auto [&>textarea]:max-h-[120px] [&>textarea]:min-h-[40px] [&>textarea]:py-[9px] [&>textarea]:pr-8 [&>textarea]:transition-all [&>textarea]:scrollbar-hide"
              onBlur={() => {
                if (!textAreaRef.current) return;
                textAreaRef.current.style.height = '40px';
              }}
              onFocus={() => {
                if (!textAreaRef.current) return;
                textAreaRef.current.style.height = 'auto';
                textAreaRef.current.style.height = textAreaRef.current.scrollHeight + 'px';
              }}
              onInput={() => {
                if (!textAreaRef.current) return;
                textAreaRef.current.style.height = 'auto';
                textAreaRef.current.style.height = textAreaRef.current.scrollHeight + 'px';
              }}
            />
            <button
              disabled={isSendingMessagesDisabled}
              onClick={() => {
                if (!textAreaRef.current) return;
                onSendMessage(textAreaRef.current.value);
              }}
              className={cn(
                'absolute bottom-[12px] right-[10px] flex size-6 items-center justify-center rounded-full bg-gray-200 -rotate-90',
                { 'opacity-35': isSendingMessagesDisabled },
              )}
            >
              <Icon cursor={isSendingMessagesDisabled ? 'default' : 'pointer'} icon="arrowRight" size="s" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
