import { Skeleton, TwButton, TypewriterText } from '@/ui/components';

import { useContentContext } from '../../content-context';

interface ReasoningProps {
  reasoning: string | null;
  showAsTypewriter?: boolean;
}

export default function Reasoning({ reasoning, showAsTypewriter }: ReasoningProps) {
  const { showExpandedPreview, setShowExpandedPreview } = useContentContext();

  if (!reasoning)
    return (
      <div className="flex w-full flex-col gap-1">
        <Skeleton className="h-6 w-[300px]" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-full" />
      </div>
    );

  return (
    <div className="flex min-h-fit w-full max-w-full items-center justify-between gap-6">
      {showAsTypewriter ? (
        <TypewriterText text={reasoning ?? ''} className="text-gray-800 body-m-regular" />
      ) : (
        <p className="text-gray-800 body-m-regular">{reasoning ?? ''}</p>
      )}
      <TwButton
        variant="tertiary-outlined"
        size="tiny"
        className="text-nowrap py-1"
        onClick={() => setShowExpandedPreview((prev) => !prev)}
      >
        {showExpandedPreview ? 'Close Full Table' : 'Preview Full Table'}
      </TwButton>
    </div>
  );
}
