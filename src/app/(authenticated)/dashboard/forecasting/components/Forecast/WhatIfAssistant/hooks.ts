import { useQueryClient } from '@tanstack/react-query';

import { useAppUsageContext } from '@/context';
import * as api from '@/services/apis';
import { ChatMessageType } from '@/ui/components';
import { toast } from '@/utils';
import { streamData } from '@/utils/stream';

import { useForecastingContext } from '../../../forecasting-context';
import { useContentContext } from '../content-context';

import * as constants from './constants';
import { useWhatIfContext } from './what-if-context';

export function useSendMessage() {
  const { activeScenarioId } = useForecastingContext();
  const { selectedNodeId, setSelectedNodeId } = useContentContext();
  const { nodelessMessages, setNodelessMessages, setStreamingInProgress, setChatLoadingMessage } = useWhatIfContext();

  const { isPromptingDisabled } = useAppUsageContext();

  const queryClient = useQueryClient();

  const addMessage = (nodeId: number | null, message: string) => {
    if (!nodeId)
      return setNodelessMessages((prev) => [
        ...prev,
        { type: ChatMessageType.RECEIVED, message, showAsTypewriter: true },
      ]);

    queryClient.setQueryData<api.WorkflowNodeData>(['workflow', 'node', activeScenarioId, nodeId], (oldData) => {
      if (!oldData) return undefined;
      return {
        ...oldData,
        conversation_history: [
          ...oldData.conversation_history,
          { source: 'AI', content: message, showAsTypewriter: true },
        ],
      };
    });
  };

  const addReasoning = (nodeId: number, reasoning: string) => {
    queryClient.setQueryData<api.WorkflowNodeData>(['workflow', 'node', activeScenarioId, nodeId], (oldData) => {
      if (!oldData) return;

      return {
        ...oldData,
        latest_reasoning: reasoning,
        show_reasoning_as_typewriter: true,
      };
    });
  };

  const addNode = (node: api.WorkflowNode, initialMessage: string) => {
    const nodes = queryClient.getQueryData<Array<api.WorkflowNode>>(['workflow', 'nodes', activeScenarioId]);

    if (nodes && nodes.find((n) => n.id === node.id)) return; // Skip adding node if it already exists

    queryClient.setQueryData<Array<api.WorkflowNode>>(['workflow', 'nodes', activeScenarioId], (oldNodes) => {
      if (!oldNodes) return [node];

      return [...oldNodes, node];
    });

    const conversationHistory = [] as Array<api.WorkflowNodeConversationItem>;
    for (const message of nodelessMessages) {
      conversationHistory.push({
        content: message.message,
        source: message.type === ChatMessageType.SENT ? 'USER' : 'AI',
      });
    }
    conversationHistory.push({ content: initialMessage, source: 'USER' });

    queryClient.setQueryData<api.WorkflowNodeData>(['workflow', 'node', activeScenarioId, node.id], () => ({
      id: node.id,
      name: node.name,
      financial_type: node.financial_type,
      conversation_history: conversationHistory,
      preview_data: [],
      latest_reasoning: null,
    }));
    setNodelessMessages([]);
  };

  const resetReasoningAndPreviewItems = () => {
    queryClient.setQueryData<api.WorkflowNodeData>(
      ['workflow', 'node', activeScenarioId, selectedNodeId],
      (oldData) => {
        if (!oldData) return undefined;
        return {
          ...oldData,
          latest_reasoning: null,
          preview_data: [],
        };
      },
    );
  };

  const addPreviewItems = (nodeId: number, previewItems: Array<api.PreviewItem>, clearExisting: boolean) => {
    queryClient.setQueryData<api.WorkflowNodeData>(['workflow', 'node', activeScenarioId, nodeId], (oldData) => {
      if (!oldData) return undefined;
      if (clearExisting) return { ...oldData, preview_data: previewItems };
      return { ...oldData, preview_data: [...(oldData.preview_data ?? []), ...previewItems] };
    });
  };

  const applyChanges = () => {
    if (!window) return;

    window.dispatchEvent(new Event('applyWhatIfChanges'));
  };

  const sendMessage = async (message: string) => {
    if (isPromptingDisabled) return;

    const addedNodeIds: Array<number> = [];
    let lastQuestion: string | null = null;
    let lastReasoning: string | null = null;

    const processStreamResponseObject = (streamResponseObject: unknown, stopStream: () => void) => {
      const workflowResponse = api.workflowResponseValidator.parse(streamResponseObject);

      const nodeId = workflowResponse.current_node_id ?? selectedNodeId;

      // Create nodes from active nodes if they don't exist
      if (workflowResponse.active_nodes && workflowResponse.active_nodes.length > 0) {
        for (const activeNode of workflowResponse.active_nodes) {
          if (!addedNodeIds.includes(activeNode.id)) {
            addedNodeIds.push(activeNode.id);
            addNode(activeNode, message);
          }
        }
      }

      if (workflowResponse.current_node_id !== selectedNodeId) {
        setSelectedNodeId(workflowResponse.current_node_id);
      }

      if (workflowResponse.generating_preview) {
        resetReasoningAndPreviewItems();
        setChatLoadingMessage(constants.ChatLoadingMessages.generatingPreview);
      }

      if (nodeId && workflowResponse.reasoning?.reasoning && workflowResponse.reasoning?.reasoning !== lastReasoning) {
        lastReasoning = workflowResponse.reasoning?.reasoning;
        addReasoning(nodeId, workflowResponse.reasoning?.reasoning);
      }

      if (workflowResponse.question && workflowResponse.question !== lastQuestion) {
        if (workflowResponse.question.includes('Applying scenario. Thank you!')) {
          applyChanges();
          return stopStream();
        } else {
          lastQuestion = workflowResponse.question;
          addMessage(nodeId, workflowResponse.question!);
        }
      }

      if (nodeId && workflowResponse.preview_line_items) {
        if (workflowResponse.current_state === 'FINISHED') {
          // Replace all preview items with the ones from final response
          addPreviewItems(nodeId, workflowResponse.preview_line_items, true);
        } else {
          addPreviewItems(nodeId, workflowResponse.preview_line_items, false);
        }
      }
    };

    setStreamingInProgress(true);
    setChatLoadingMessage(constants.ChatLoadingMessages.default);

    await streamData({
      apiFunction: () => api.runWorkflow(activeScenarioId!, { userInput: message, currentNodeId: selectedNodeId }),
      processStreamResponseObject,
      onError: (error) => {
        const isAppUsageError = (error as Error).message === 'appUsage';

        if (isAppUsageError) toast.error('Action not available in the current plan.');
        else toast.error('Something went wrong');
      },
    });

    setStreamingInProgress(false);
  };

  return { sendMessage };
}

export function useOnSendMessage() {
  const { activeScenarioId } = useForecastingContext();
  const { selectedNodeId, setAutoExpandedNodes, setShowExpandedPreview } = useContentContext();
  const queryClient = useQueryClient();

  const { sendMessage } = useSendMessage();

  return {
    onSendMessage: async (message: string) => {
      queryClient.setQueryData<api.WorkflowNodeData>(
        ['workflow', 'node', activeScenarioId, selectedNodeId],
        (oldData) => {
          if (!oldData) return undefined;

          return {
            ...oldData,
            conversation_history: [...oldData.conversation_history, { source: 'USER', content: message }],
          };
        },
      );

      setShowExpandedPreview(false);
      setAutoExpandedNodes(false);
      await sendMessage(message);
    },
  };
}
