import { Fragment } from 'react';

import { financialType } from '@/constants';
import * as api from '@/services/apis';
import { Icon } from '@/ui/components';
import { cn } from '@/ui/utils';

import { useContentContext } from '../content-context';

import { useWhatIfContext } from './what-if-context';

interface NodeListProps {
  nodes: Array<api.WorkflowNode>;
}

export default function NodeList({ nodes }: NodeListProps) {
  const { selectedNodeId, setSelectedNodeId } = useContentContext();
  const { streamingInProgress } = useWhatIfContext();

  if (nodes.length === 0) return null;

  return (
    <>
      <div className="relative min-h-8 w-full overflow-hidden">
        <div className="w-full overflow-auto overscroll-x-none px-1 py-6 scrollbar-hide">
          <div className="flex w-fit items-center pr-[200px]">
            {nodes.map((node, index) => (
              <Fragment key={node.id}>
                <button
                  key={node.id}
                  className={cn(
                    'flex items-center gap-1 text-nowrap rounded bg-gray-200 hover:bg-gray-300 px-1 py-[2px] outline-gray-200 body-m-medium focus:outline focus:bg-gray-400',
                    { 'outline-turquoise-200 bg-turquoise-400 focus:bg-turquoise-400': selectedNodeId === node.id },
                  )}
                  onClick={() => {
                    if (selectedNodeId === node.id) return;
                    setSelectedNodeId(node.id);
                  }}
                  disabled={streamingInProgress}
                >
                  {node.financial_type && (
                    <span className="text-gray-500">
                      {node.financial_type === financialType.INCOME ? 'Income' : 'Expenses'} /
                    </span>
                  )}
                  <span className="text-turquoise-900">{node.name}</span>
                </button>
                {index !== nodes.length - 1 && (
                  <Icon icon="chevron" className="text-gray-500 -rotate-90" cursor="default" />
                )}
              </Fragment>
            ))}
          </div>
        </div>
        <div className="pointer-events-none absolute right-0 top-0 h-8 w-[200px] bg-gradient-to-r from-transparent to-gray-100" />
      </div>
    </>
  );
}
