import min from 'lodash/min';

import * as api from '@/services/apis';
import { FinancialType } from '@/types';

import * as helpers from '../../../../helpers';
import { ForecastLevel, ProcessedForecastData } from '../../../../types';

const distanceBetweenPeriods = (period1: string, period2: string) => {
  const [year1, month1] = period1.split('-');
  const [year2, month2] = period2.split('-');

  const diffYears = Number(year2) - Number(year1);
  const diffMonths = Number(month2) - Number(month1);

  return diffYears * 12 + diffMonths;
};

// Generate array from current period to the last period seen in edit changes.
// Minimum length is 12 periods.
export const getPeriods = (editChanges: Array<api.EditChange>) => {
  let endIndex = 12;

  let lastSeenPeriod = editChanges?.[0]?.period;
  for (const editChange of editChanges) {
    if (lastSeenPeriod && editChange.period <= lastSeenPeriod) continue;
    lastSeenPeriod = editChange.period;
  }

  const today = new Date();

  if (lastSeenPeriod) {
    const currentPeriod = helpers.getCurrentTimePeriod();
    const distance = distanceBetweenPeriods(currentPeriod, lastSeenPeriod) + 1;
    if (distance > endIndex) endIndex = distance;
  }

  const periods = [];
  for (let i = 0; i < endIndex; i++) {
    const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    periods.push(`${date.getFullYear()}-${month}`);
  }

  return periods;
};

export function findFirstPeriodWithData(dataPerType: Record<FinancialType, ForecastLevel>) {
  const entries = [
    ...Object.entries(dataPerType.INCOME?.forecastedData ?? []),
    ...Object.entries(dataPerType.EXPENSE?.forecastedData ?? []),
  ];

  if (entries.length === 0) return undefined;

  const filteredPeriods = entries
    .filter(([_period, dataPoint]) => !isNaN(dataPoint.amount))
    .map(([period, _dataPoint]) => period);

  return min(filteredPeriods);
}

function expandRecursively(
  data: ForecastLevel,
  setWhatIfExpandedRows: React.Dispatch<React.SetStateAction<Array<string>>>,
) {
  if (Object.values(data.forecastedData).some(({ isHighlighted }) => isHighlighted)) {
    setWhatIfExpandedRows((prev) => [...prev, `${data.id}-${data.name}-${data.leafCategoryId}`]);
  }

  if (data.children) {
    for (const child of data.children) {
      expandRecursively(child, setWhatIfExpandedRows);
    }
  }
}

export function expandChangedRows(
  processedForecastData: ProcessedForecastData,
  setWhatIfExpandedRows: React.Dispatch<React.SetStateAction<Array<string>>>,
) {
  setWhatIfExpandedRows([]);
  if (processedForecastData.income) expandRecursively(processedForecastData.income, setWhatIfExpandedRows);
  if (processedForecastData.expense) expandRecursively(processedForecastData.expense, setWhatIfExpandedRows);
}
