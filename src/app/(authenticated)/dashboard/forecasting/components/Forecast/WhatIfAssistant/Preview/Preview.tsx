import { useCallback, useEffect, useMemo } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'framer-motion';

import { useForecastingContext } from '@/app/(authenticated)/dashboard/forecasting/forecasting-context';
import { useAppUsageContext } from '@/context';
import * as api from '@/services/apis';
import { Skeleton, TwButton } from '@/ui/components';
import { toast } from '@/utils';

import { useContentContext } from '../../content-context';
import * as helpers from '../../helpers';
import { useForecastHierarchy, useProcessedForecastData } from '../../hooks';
import { Table } from '../../Table';

import * as previewHelpers from './helpers';
import { useTransformEditChangesToAccountForecastData, useTransformPreviewItemsToEditChanges } from './hooks';
import PreviewTable from './PreviewTable';
import Reasoning from './Reasoning';

interface PreviewProps {
  workflowNode: api.WorkflowNodeData;
  forecastData: api.ForecastData;
  streamingInProgress: boolean;
}

export default function Preview({ workflowNode, forecastData, streamingInProgress }: PreviewProps) {
  const { activeScenarioId } = useForecastingContext();
  const {
    endEditMode,
    showExpandedPreview,
    setShowExpandedPreview,
    whatIfExpandedRows,
    setWhatIfExpandedRows,
    whatIfPreviewCollapsedRows,
    setWhatIfPreviewCollapsedRows,
    selectedNodeId,
    setSelectedNodeId,
    autoExpandedNodes,
    setAutoExpandedNodes,
  } = useContentContext();

  const { isMutationDisabled } = useAppUsageContext();

  const editChanges = useTransformPreviewItemsToEditChanges(workflowNode.preview_data ?? []);
  const accountForecastData = useTransformEditChangesToAccountForecastData(editChanges);
  const previewDataPerType = useForecastHierarchy(accountForecastData);

  const { processedForecastData } = useProcessedForecastData(forecastData, editChanges, false, accountForecastData);

  const changedPeriods = useMemo(() => {
    if (editChanges.length === 0) return [];

    return editChanges.map((editChange) => editChange.period);
  }, [editChanges]);

  const earliestChangedPeriod = useMemo(() => {
    if (changedPeriods.length === 0) return undefined;

    return changedPeriods.sort()[0];
  }, [changedPeriods]);

  const queryClient = useQueryClient();

  const periods = previewHelpers.getPeriods(editChanges);

  const noPreviewData = !previewDataPerType.INCOME && !previewDataPerType.EXPENSE;

  const { mutateAsync: removeWorkflowNode, isPending: isRemoveWorkflowNodePending } = useMutation({
    mutationFn: () => api.removeWorkflowNode(activeScenarioId!, selectedNodeId!),
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: ['workflow', 'nodes', activeScenarioId] });
      setSelectedNodeId(null);
      setShowExpandedPreview(false);
    },
  });

  const { mutate: updateForecastData, isPending: isUpdatingForecastData } = useMutation({
    mutationFn: ({ forecastId, data }: { forecastId: number; data: api.UpdateForecastDataData }) =>
      api.updateForecastData(forecastId, data),
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['forecast', activeScenarioId] });
      toast.success('Successfully applied changes to scenario.');

      await removeWorkflowNode();

      endEditMode();
    },
  });

  const saveChanges = useCallback(() => {
    const scenarioId = Number(activeScenarioId);
    if (isNaN(scenarioId)) return toast.error('Something went wrong. Please try again.');

    const preparedData = helpers.prepareEditChangesForApi(editChanges, false);
    updateForecastData({ forecastId: scenarioId, data: preparedData });
  }, [activeScenarioId, editChanges, updateForecastData]);

  useEffect(() => {
    if (!selectedNodeId || streamingInProgress || autoExpandedNodes) return;
    previewHelpers.expandChangedRows(processedForecastData, setWhatIfExpandedRows);
    setAutoExpandedNodes(true);
  }, [
    autoExpandedNodes,
    processedForecastData,
    selectedNodeId,
    setAutoExpandedNodes,
    setWhatIfExpandedRows,
    streamingInProgress,
  ]);

  useEffect(() => {
    window.addEventListener('applyWhatIfChanges', saveChanges);

    return () => {
      window.removeEventListener('applyWhatIfChanges', saveChanges);
    };
  }, [saveChanges]);

  return (
    <div className="flex grow flex-col gap-3 overflow-hidden rounded-xl border border-gray-200 bg-white p-6 pt-8">
      <div className="flex grow overflow-hidden">
        {noPreviewData && !streamingInProgress && (
          <p className="flex w-full items-center justify-center text-gray-500 body-s-regular">
            Your data will be previewed here
          </p>
        )}
        {(!noPreviewData || streamingInProgress) && (
          <div className="flex w-full flex-col gap-8 overflow-hidden">
            <Reasoning
              reasoning={workflowNode.latest_reasoning}
              showAsTypewriter={workflowNode.show_reasoning_as_typewriter}
            />
            {noPreviewData && streamingInProgress && <Skeleton className="h-[180px] w-full" />}
            {!noPreviewData && (
              <div className="relative size-full">
                <AnimatePresence>
                  {!showExpandedPreview && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="absolute inset-0 size-full"
                    >
                      <PreviewTable
                        dataPerType={previewDataPerType}
                        periods={periods}
                        currency={forecastData.currency}
                        collapsedRows={whatIfPreviewCollapsedRows}
                        toggleCollapsedRow={(name) => {
                          if (whatIfPreviewCollapsedRows.includes(name)) {
                            setWhatIfPreviewCollapsedRows((prev) => prev.filter((rowName) => rowName !== name));
                          } else setWhatIfPreviewCollapsedRows((prev) => [...prev, name]);
                        }}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
                <AnimatePresence>
                  {showExpandedPreview && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="absolute inset-0 size-full overflow-hidden"
                    >
                      <Table
                        forecast={processedForecastData}
                        expandedRows={whatIfExpandedRows}
                        toggleExpandedRow={(name) => {
                          if (whatIfExpandedRows.includes(name)) {
                            setWhatIfExpandedRows((prev) => prev.filter((rowName) => rowName !== name));
                          } else setWhatIfExpandedRows((prev) => [...prev, name]);
                        }}
                        backgroundColor="bg-white"
                        disableEditing
                        earliestChangedPeriod={earliestChangedPeriod}
                        changedPeriods={changedPeriods}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="flex items-center gap-2 self-end">
        <button
          className="px-2 py-1 text-gray-600 body-m-medium disabled:opacity-35"
          onClick={() => removeWorkflowNode()}
          disabled={isRemoveWorkflowNodePending || isUpdatingForecastData || streamingInProgress || isMutationDisabled}
        >
          Remove
        </button>
        <TwButton
          size="small"
          onClick={saveChanges}
          disabled={
            isRemoveWorkflowNodePending ||
            isUpdatingForecastData ||
            noPreviewData ||
            streamingInProgress ||
            isMutationDisabled
          }
        >
          Apply Changes
        </TwButton>
      </div>
    </div>
  );
}
