import { useEffect, useRef, useState } from 'react';

import { Currency, FinancialType } from '@/types';
import { cn } from '@/ui/utils';

import * as forecastingHelpers from '../../../../helpers';
import { ForecastLevel } from '../../../../types';

import * as constants from './constants';
import { findFirstPeriodWithData } from './helpers';
import Row from './Row';

interface PreviewTableProps {
  periods: Array<string>;
  dataPerType: Record<FinancialType, ForecastLevel>;
  currency: Currency;
  collapsedRows: Array<string>;
  toggleCollapsedRow: (rowName: string) => void;
}

export default function PreviewTable({
  dataPerType,
  periods,
  currency,
  collapsedRows,
  toggleCollapsedRow,
}: PreviewTableProps) {
  const [hoveredColumnIndex, setHoveredColumnIndex] = useState<number | null>(null);
  const previewTableRef = useRef<HTMLDivElement>(null);

  const currentTimePeriodIndex = forecastingHelpers.getCurrentTimePeriodIndex(periods);

  const firstPeriodWithData = findFirstPeriodWithData(dataPerType);
  const firstPeriodWithDataIndex = firstPeriodWithData ? periods.indexOf(firstPeriodWithData) : undefined;

  useEffect(() => {
    if (previewTableRef.current && firstPeriodWithDataIndex)
      previewTableRef.current.scrollTo(firstPeriodWithDataIndex * constants.cellWidth, 0);
  }, [firstPeriodWithDataIndex]);

  return (
    <section
      ref={previewTableRef}
      className="relative flex h-fit max-h-full flex-col overflow-auto overscroll-none pb-4 scrollbar-hide"
    >
      <div className="sticky top-0 z-20 flex w-full min-w-fit bg-white pr-[2px] text-gray-500 body-compact-s">
        <div className="sticky left-0 w-[350px] min-w-[350px] max-w-[350px] bg-white" />
        {periods.map((period, index) => (
          <div
            key={index}
            className={cn('flex flex-1 items-end justify-center bg-white pb-[3px] mb-[10px] pt-[7px] text-center', {
              'bg-purple-100/30': index > currentTimePeriodIndex,
              'bg-purple-200/30': index === currentTimePeriodIndex || hoveredColumnIndex === index,
            })}
            style={{ minWidth: `${constants.cellWidth}px` }}
            onMouseEnter={() => setHoveredColumnIndex(index)}
            onMouseLeave={() => setHoveredColumnIndex(null)}
          >
            {forecastingHelpers.formatPeriod(period)}
          </div>
        ))}
      </div>
      <div className="flex flex-col gap-1">
        {dataPerType.INCOME && (
          <Row
            data={{ ...dataPerType.INCOME, name: 'Income' }}
            periods={periods}
            currency={currency}
            isFirstRow
            collapsedRows={collapsedRows}
            toggleCollapsedRow={toggleCollapsedRow}
            hoveredColumnIndex={hoveredColumnIndex}
            setHoveredColumnIndex={setHoveredColumnIndex}
          />
        )}
        {dataPerType.EXPENSE && (
          <Row
            data={{ ...dataPerType.EXPENSE, name: 'Expense' }}
            periods={periods}
            currency={currency}
            collapsedRows={collapsedRows}
            toggleCollapsedRow={toggleCollapsedRow}
            hoveredColumnIndex={hoveredColumnIndex}
            setHoveredColumnIndex={setHoveredColumnIndex}
          />
        )}
      </div>
    </section>
  );
}
