import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';

import { useAppUsageContext } from '@/context';
import * as api from '@/services/apis';
import { ChatInterface, ChatMessageType, ErrorSection, Skeleton } from '@/ui/components';
import { cn } from '@/ui/utils';

import { useForecastingContext } from '../../../forecasting-context';
import { useContentContext } from '../content-context';

import { useOnSendMessage } from './hooks';
import NodeList from './NodeList';
import OptionsBar from './OptionsBar';
import { Preview } from './Preview';
import { useWhatIfContext } from './what-if-context';
interface NodeViewProps {
  nodes: Array<api.WorkflowNode>;
  forecastData: api.ForecastData;
}

export default function NodeView({ nodes, forecastData }: NodeViewProps) {
  const { activeScenarioId } = useForecastingContext();
  const { selectedNodeId, showExpandedPreview } = useContentContext();
  const { streamingInProgress, chatLoadingMessage } = useWhatIfContext();

  const { isPromptingDisabled } = useAppUsageContext();

  const { onSendMessage } = useOnSendMessage();

  const {
    data: workflowNode,
    isPending: isWorkflowNodePending,
    isError: isWorkflowNodeError,
    refetch: refetchWorkflowNode,
  } = useQuery({
    queryKey: ['workflow', 'node', activeScenarioId, selectedNodeId],
    queryFn: () => api.getWorkflowNodeData(activeScenarioId!, selectedNodeId!),
    enabled: !!activeScenarioId && !!selectedNodeId,
  });

  if (isWorkflowNodePending) {
    return (
      <section className="flex size-full h-full gap-4">
        <Skeleton className="min-w-[325px] max-w-[325px]" />
        <div className="flex grow flex-col gap-4 overflow-hidden">
          <div className="relative flex grow flex-col overflow-hidden">
            <NodeList nodes={nodes} />
            <Skeleton className="flex grow rounded-xl" />
          </div>
        </div>
      </section>
    );
  }

  if (isWorkflowNodeError) {
    return (
      <section className="flex size-full h-full gap-4">
        <ChatInterface
          disabled
          messages={[]}
          onSendMessage={async () => {}}
          className="min-w-[325px] max-w-[325px] rounded-xl border border-gray-200 bg-white"
          receivedMessageOptions={{ backgroundColor: 'bg-gray-100' }}
        />
        <div className="flex grow flex-col gap-4 overflow-hidden">
          <div className="relative flex grow flex-col overflow-hidden">
            <NodeList nodes={nodes} />
            <ErrorSection className="rounded-xl border border-gray-200 bg-white pb-0" resetFn={refetchWorkflowNode} />
          </div>
        </div>
      </section>
    );
  }

  const messages = workflowNode.conversation_history.map((item) => ({
    type: item.source === 'AI' ? ChatMessageType.RECEIVED : ChatMessageType.SENT,
    message: item.content,
    showAsTypewriter: item.showAsTypewriter,
  }));

  return (
    <section className="relative flex size-full h-full gap-4">
      <motion.div
        animate={{ opacity: showExpandedPreview ? 0 : 1 }}
        transition={{ duration: 0.2, ease: 'linear' }}
        className="absolute left-0 top-0 h-full overflow-hidden"
      >
        <ChatInterface
          messages={messages}
          disabled={isPromptingDisabled}
          onSendMessage={onSendMessage}
          loading={streamingInProgress}
          loadingMessage={chatLoadingMessage}
          className="min-w-[325px] max-w-[325px] rounded-xl border border-gray-200 bg-white"
          receivedMessageOptions={{ backgroundColor: 'bg-gray-100' }}
        />
      </motion.div>
      <motion.div
        className={cn(
          'absolute right-0 top-0 flex w-[calc(100%-341px)] h-full flex-col gap-4 overflow-hidden transition-all duration-200 ease-linear max-w-[calc(100%-341px)]',
          {
            'max-w-full w-full': showExpandedPreview,
          },
        )}
      >
        <div className="relative flex grow flex-col overflow-hidden bg-gray-100">
          <OptionsBar />
          <NodeList nodes={nodes} />
          <Preview forecastData={forecastData} streamingInProgress={streamingInProgress} workflowNode={workflowNode} />
        </div>
      </motion.div>
    </section>
  );
}
