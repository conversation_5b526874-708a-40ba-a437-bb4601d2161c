import { Dispatch, SetStateAction } from 'react';

import { nDash } from '@/constants';
import { useAuthContext } from '@/context';
import { Currency } from '@/types';
import { cn } from '@/ui/utils';
import { formatCurrencyUtils } from '@/utils';

import * as constants from './constants';

interface CellProps {
  index: number;
  depth: number;
  amount: number | undefined;
  currency: Currency;
  isRowExpanded: boolean;
  isLastCell?: boolean;
  isInHoveredRow?: boolean;
  isInHoveredColumn?: boolean;
  isCurrentTimePeriod?: boolean;
  setHoveredColumnIndex: Dispatch<SetStateAction<number | null>>;
}

export default function Cell({
  index,
  depth,
  amount,
  currency,
  isRowExpanded,
  isLastCell = false,
  isInHoveredRow,
  isInHoveredColumn,
  isCurrentTimePeriod,
  setHoveredColumnIndex,
}: CellProps) {
  const { company } = useAuthContext();

  return (
    <div
      data-container="cell"
      className={cn('h-full flex-1 bg-white', {
        'bg-purple-100/30': isInHoveredRow && !isCurrentTimePeriod,
        '!bg-purple-200/30': isInHoveredColumn || (isInHoveredRow && isCurrentTimePeriod),
      })}
      style={{ minWidth: `${constants.cellWidth}px` }}
      onMouseEnter={() => setHoveredColumnIndex(index)}
      onMouseLeave={() => setHoveredColumnIndex(null)}
    >
      <div
        className={cn('w-full h-full flex items-center justify-center border-b border-gray-200 group/cell', {
          'border-r': isLastCell,
          'border-t': depth === 0,
          'rounded-tr-lg': depth === 0 && isLastCell,
          'rounded-br-lg': depth === 0 && !isRowExpanded && isLastCell,
        })}
      >
        <div className="flex size-full items-center justify-center body-m-regular">
          <div className="flex w-fit items-center justify-center">
            <span className="size-full rounded px-1 text-center text-gray-900">
              {formatCurrencyUtils.formatCurrency(amount, currency, company?.decimalPoint, {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
                fallbackValue: nDash,
              })}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
