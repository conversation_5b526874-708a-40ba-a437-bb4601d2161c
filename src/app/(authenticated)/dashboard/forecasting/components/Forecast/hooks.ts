import { useMemo } from 'react';

import { financialType } from '@/constants';
import { useLeafCategories } from '@/hooks';
import * as api from '@/services/apis';
import { FinancialType } from '@/types';

import { AccountForecastData, ForecastLevel, ProcessedForecastData } from '../../types';

import * as helpers from './helpers';

export function useForecastHierarchy(accounts: Array<AccountForecastData>) {
  const { leafCategoriesById } = useLeafCategories();

  const dataPerType = useMemo(() => {
    const result = {} as Record<FinancialType, ForecastLevel>;
    for (const account of accounts) {
      const leafCategoryData = leafCategoriesById[account.leafCategoryId];

      if (!leafCategoryData) continue;

      const {
        type,
        id: leafCategoryId,
        name: leafCategoryName,
        category: { id: categoryId, name: categoryName },
      } = leafCategoryData;

      let forecastedData = account.forecastedData;
      let actualData = account.actualData;
      if (type === financialType.EXPENSE) {
        forecastedData = structuredClone(account.forecastedData);
        Object.keys(forecastedData).forEach((period) => {
          forecastedData[period].amount *= -1;
        });

        actualData = structuredClone(account.actualData);
        Object.keys(actualData).forEach((period) => {
          actualData[period].amount *= -1;
        });
      }

      if (!result[type]) {
        result[type] = {
          name: type.toLowerCase(),
          forecastedData: {},
          actualData: {},
          children: [],
        };
      }

      let category = result[type].children.find((child) => child.id === categoryId);

      if (!category) {
        category = {
          id: categoryId,
          name: categoryName,
          forecastedData: {},
          actualData: {},
          children: [],
        };
        result[type].children.push(category);
      }

      let leafCategory = category.children.find((child) => child.id === leafCategoryId);

      if (!leafCategory) {
        leafCategory = {
          id: leafCategoryId,
          name: leafCategoryName,
          forecastedData: {},
          actualData: {},
          children: [],
        };
        category.children.push(leafCategory);
      }

      leafCategory.children.push({
        id: account.accountId ?? undefined,
        name: account.accountName,
        leafCategoryId: account.leafCategoryId,
        forecastedData: forecastedData,
        actualData: actualData,
        children: [],
      });

      Object.keys(forecastedData).forEach((period) => {
        const amount = forecastedData[period].amount;
        const isHighlighted = forecastedData[period].isHighlighted;

        if (!leafCategory.forecastedData[period]) {
          leafCategory.forecastedData[period] = {
            amount: 0,
            isHighlighted: false,
          };
        }

        leafCategory.forecastedData[period].amount += amount;
        if (isHighlighted) leafCategory.forecastedData[period].isHighlighted = true;

        if (!category.forecastedData[period]) {
          category.forecastedData[period] = {
            amount: 0,
            isHighlighted: false,
          };
        }
        category.forecastedData[period].amount += amount;
        if (isHighlighted) category.forecastedData[period].isHighlighted = true;

        if (!result[type].forecastedData[period]) {
          result[type].forecastedData[period] = {
            amount: 0,
            isHighlighted: false,
          };
        }
        result[type].forecastedData[period].amount += amount;
        if (isHighlighted) result[type].forecastedData[period].isHighlighted = true;
      });

      Object.keys(actualData).forEach((period) => {
        const amount = actualData[period].amount;

        if (!leafCategory.actualData[period]) {
          leafCategory.actualData[period] = { amount: 0 };
        }

        leafCategory.actualData[period].amount += amount;

        if (!category.actualData[period]) {
          category.actualData[period] = {
            amount: 0,
          };
        }
        category.actualData[period].amount += amount;

        if (!result[type].actualData[period]) {
          result[type].actualData[period] = {
            amount: 0,
          };
        }
        result[type].actualData[period].amount += amount;
      });
    }

    return result;
  }, [accounts, leafCategoriesById]);

  return dataPerType;
}

export function useProcessedForecastData(
  forecastData: api.ForecastData,
  editChanges: Array<api.EditChange>,
  shouldFlipEditChanges: boolean,
  additionalAccountsData?: Array<AccountForecastData>,
): { processedForecastData: ProcessedForecastData; accountsWithEditChanges: Array<AccountForecastData> } {
  const accountsWithEditChanges = useMemo(() => {
    const newAccountsData = [];
    if (additionalAccountsData) {
      for (const account of additionalAccountsData) {
        const existingAccount = forecastData.accounts.find(
          (acc) => acc.accountName === account.accountName && acc.leafCategoryId === account.leafCategoryId,
        );
        if (existingAccount) continue;

        newAccountsData.push(account);
      }
    }

    return helpers.applyEditChangesToAccounts(
      [...forecastData.accounts, ...newAccountsData],
      editChanges,
      shouldFlipEditChanges,
    );
  }, [additionalAccountsData, forecastData.accounts, editChanges, shouldFlipEditChanges]);

  const dataPerType = useForecastHierarchy(accountsWithEditChanges);

  let actualIncomeData = {} as Record<string, { amount: number }>;
  let forecastedIncomeData = {} as Record<string, { amount: number }>;
  let actualExpenseData = {} as Record<string, { amount: number }>;
  let forecastedExpenseData = {} as Record<string, { amount: number }>;

  if (dataPerType[financialType.INCOME]) {
    actualIncomeData = dataPerType[financialType.INCOME].actualData;
    forecastedIncomeData = dataPerType[financialType.INCOME].forecastedData;
  }
  if (dataPerType[financialType.EXPENSE]) {
    actualExpenseData = dataPerType[financialType.EXPENSE].actualData;
    forecastedExpenseData = dataPerType[financialType.EXPENSE].forecastedData;
  }

  // Initialize the base structure
  const processedForecastData = {
    id: forecastData.id,
    isBase: forecastData.isBase,
    isTracked: forecastData.isTracked,
    updatedAt: forecastData.updatedAt,
    currency: forecastData.currency,
    periods: forecastData.periods,
    expense: dataPerType[financialType.EXPENSE]
      ? {
          id: 0,
          name: 'Expense',
          forecastedData: dataPerType[financialType.EXPENSE].forecastedData,
          actualData: dataPerType[financialType.EXPENSE].actualData,
          children: dataPerType[financialType.EXPENSE].children,
        }
      : undefined,
    income: dataPerType[financialType.INCOME]
      ? {
          id: 0,
          name: 'Income',
          forecastedData: dataPerType[financialType.INCOME].forecastedData,
          actualData: dataPerType[financialType.INCOME].actualData,
          children: dataPerType[financialType.INCOME].children,
        }
      : undefined,
    balancesAndNetChanges: helpers.calculateBalancesAndNetChanges(
      forecastData.periods,
      forecastData.balancesData,
      actualIncomeData,
      forecastedIncomeData,
      actualExpenseData,
      forecastedExpenseData,
    ),
  };

  return { accountsWithEditChanges, processedForecastData };
}
