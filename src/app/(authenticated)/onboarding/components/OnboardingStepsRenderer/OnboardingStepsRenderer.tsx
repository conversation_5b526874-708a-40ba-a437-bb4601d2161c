'use client';

import { useOnboardingContext } from '@/app/(authenticated)/onboarding/context';
import { Turnstile } from '@/ui/components';

import { OnboardingSteps } from '../../types';
import { CompanyInfo, UserInfo } from '../steps';

import Navigation from './Navigation';
import PrivacyPolicy from './PrivacyPolicy';
import { Stepper } from './Stepper';

export default function OnboardingStepsRenderer() {
  const { step, navigationOptions, setTurnstileToken, turnstileRef } = useOnboardingContext();

  return (
    <div className="grid grow grid-rows-[min-content_minmax(0,1fr)_min-content]">
      <Stepper />

      <div className="z-10 mb-7">
        {step === OnboardingSteps.USER_INFO && <UserInfo />}
        {step === OnboardingSteps.COMPANY_INFO && <CompanyInfo />}
      </div>

      {step === OnboardingSteps.COMPANY_INFO && (
        <div className="mb-6 w-[320px] hmd:w-[400px]">
          <Turnstile ref={turnstileRef} action="registration" setTurnstileToken={setTurnstileToken} />
        </div>
      )}

      <Navigation
        onBack={navigationOptions?.onBack}
        onNext={navigationOptions?.onNext}
        nextButtonText={navigationOptions?.nextButtonText}
        backDisabled={navigationOptions?.backDisabled}
        nextLoading={navigationOptions?.nextLoading}
        nextDisabled={navigationOptions?.nextDisabled}
        iconRight={navigationOptions?.iconRight}
      />

      <PrivacyPolicy step={step} />
    </div>
  );
}
