'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';

import { type OnboardingCompany, useOnboardingContext } from '@/app/(authenticated)/onboarding/context';
import { config } from '@/config';
import { useAuthContext } from '@/context';
import { useBreakpoint, useCountries, useNavigation } from '@/hooks';
import { Routes } from '@/routes';
import * as api from '@/services/apis';
import { ControlledSingleDropdown, ErrorSection, Loader, TextInput } from '@/ui/components';
import { turnstileErrorHandler } from '@/utils';

import { OnboardingSteps } from '../../../types';
import StepContainer from '../../StepContainer';
import { useCompanyTypeOptions, useIndustryGroupOptions } from '../hooks';

import { type CompanyInfoInput, companyInfoValidator } from './CompanyInfo.validators';

export default function CompanyInfo() {
  const {
    prevStep,
    setStep,
    setNavigationOptions,
    company,
    setCompany,
    user,
    turnstileToken,
    setTurnstileToken,
    turnstileRef,
  } = useOnboardingContext();
  const { countries, countriesIsPending, countriesIsError } = useCountries();
  const { industryOptions, isIndustryOptionsPending, isIndustryOptionsError } = useIndustryGroupOptions();
  const { setAuthData } = useAuthContext();
  const { router } = useNavigation();

  const { isHMD, isCHsm, isCHxl } = useBreakpoint(undefined, { sm: 800, xl: 1200 });

  const {
    control,
    register,
    watch,
    resetField,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<CompanyInfoInput>({
    mode: 'onChange',
    defaultValues: {
      name: company.name ?? '',
      address: company.address ?? '',
      countryId: company.countryId,
      typeId: company.typeId,
      industryId: company.industryId,
    },
    resolver: zodResolver(companyInfoValidator),
  });

  const { mutate: submitRegistration, isPending: isRegisterPending } = useMutation({
    mutationFn: () => api.registration({ user, company } as api.RegistrationInput, turnstileToken),
    onSuccess: (response) => {
      setAuthData(response.data);
      router.push(
        response.redirectBaseUrl === config.app.baseUrl
          ? Routes.DASHBOARD
          : `${response.redirectBaseUrl}${Routes.DASHBOARD}`,
      );
    },
    onError: (error) => turnstileErrorHandler(error, turnstileRef, setTurnstileToken),
  });

  const countryId = watch('countryId');
  const typeId = watch('typeId');
  const isFormValid = isValid && typeId != null;

  const { companyTypeOptions, companyTypePending, companyTypeError, refetchCompanyTypes } =
    useCompanyTypeOptions(countryId);

  useEffect(() => {
    if (countryId && countryId !== company.countryId) {
      refetchCompanyTypes();
      resetField('typeId', { defaultValue: null });
    }
  }, [countryId, company.countryId, refetchCompanyTypes, resetField]);

  useEffect(() => {
    setNavigationOptions({
      onNext: handleSubmit((data) => {
        setCompany({ ...company, ...data } as OnboardingCompany);
        submitRegistration();
      }),
      onBack: () => setStep(OnboardingSteps.USER_INFO),
      nextDisabled: !isFormValid || !turnstileToken,
      nextLoading: isRegisterPending,
      nextButtonText: 'Finish',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFormValid, isRegisterPending, turnstileToken]);

  useEffect(() => {
    return () => {
      setTurnstileToken(config.app.environment === 'local' ? 'mock' : '');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <StepContainer hasEnterAnimation={false} direction={prevStep === OnboardingSteps.REVIEW ? 'reverse' : 'normal'}>
      {(countriesIsError || isIndustryOptionsError || companyTypeError) && <ErrorSection />}
      {(countriesIsPending || isIndustryOptionsPending || companyTypePending) && <Loader />}
      {!!countries && !!industryOptions && !!companyTypeOptions && (
        <form className="flex grow gap-12 overflow-visible hlg:flex-col hlg:gap-0">
          <div className="flex w-[320px] flex-col hmd:w-[400px]">
            <TextInput {...register('name')} label="Company Name" error={errors.name?.message} autoFocus />

            <TextInput {...register('address')} label="Company Address" error={errors.address?.message} />

            <ControlledSingleDropdown
              name="countryId"
              options={countries?.map(({ id, name, flagEmoji }) => ({ value: id, label: `${flagEmoji} ${name}` }))}
              label="Country"
              control={control}
              className="mb-9"
              maxMenuHeight={!isHMD ? 175 : 250}
              menuPlacement={!isCHsm ? 'top' : 'bottom'}
              isLoading={countriesIsPending}
            />
          </div>

          <div className="flex w-[320px] flex-col hmd:w-[400px]">
            <ControlledSingleDropdown
              name="typeId"
              options={companyTypeOptions?.map(({ id, name }) => ({ value: id, label: name }))}
              label="Company Type"
              control={control}
              className="mb-9"
              isDisabled={!countryId}
              maxMenuHeight={!isHMD ? 175 : 250}
              menuPlacement={!isCHsm ? 'top' : 'bottom'}
              isLoading={companyTypePending}
            />

            <ControlledSingleDropdown
              name="industryId"
              label="Company Industry"
              control={control}
              options={industryOptions}
              menuPlacement={!isHMD || isCHxl ? 'bottom' : 'top'}
              maxMenuHeight={!isHMD ? 175 : 250}
              isLoading={isIndustryOptionsPending}
            />
          </div>
        </form>
      )}
    </StepContainer>
  );
}
