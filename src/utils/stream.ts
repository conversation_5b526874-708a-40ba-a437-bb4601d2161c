import * as Sentry from '@sentry/nextjs';

interface HandleStreamProps {
  reader: ReadableStreamDefaultReader;
  processStreamResponseObject: (streamResponseObject: unknown, stopStream: () => void) => void;
}

async function handleStream({ reader, processStreamResponseObject }: HandleStreamProps) {
  const decoder = new TextDecoder();

  // eslint-disable-next-line no-constant-condition
  while (true) {
    const { done, value } = await reader.read();

    if (done) return;

    const streamResponseString = decoder.decode(value, { stream: true });

    const streamResponseObjects = streamResponseString
      .trim()
      .split('\n')
      .map((objString) => JSON.parse(objString));

    const stopStream = async () => {
      await reader?.cancel();
    };

    for (const streamResponseObject of streamResponseObjects) {
      processStreamResponseObject(streamResponseObject, stopStream);
    }
  }
}

interface StreamDataProps {
  apiFunction: () => Promise<Response>;
  processStreamResponseObject: (streamResponseObject: unknown, stopStream: () => void) => void;
  onError?: (error: unknown) => void;
}

export async function streamData({ apiFunction, processStreamResponseObject, onError }: StreamDataProps) {
  let reader;

  try {
    const response = await apiFunction();

    if (!response.ok || !response.body) {
      if (response.status === 403) {
        window.dispatchEvent(new Event('fetchUser'));
        throw new Error('appUsage');
      }

      throw new Error('failure');
    }

    reader = response.body.getReader();

    await handleStream({ reader, processStreamResponseObject });
  } catch (error) {
    onError?.(error);

    Sentry.captureException(error);
    await reader?.cancel();
  }
}
