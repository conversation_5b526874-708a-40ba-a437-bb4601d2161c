import { aiDisplayQueryDataType } from '@/constants';
import type {
  AiDisplayQueryBalanceDataItem,
  AiDisplayQueryBillDataItem,
  AiDisplayQueryCustomDataItem,
  AiDisplayQueryInvoiceDataItem,
  AiDisplayQueryTransactionDataItem,
} from '@/types';

export type AlertNotification = {
  id: number;
  title: string;
  isViewed: boolean;
  dataKey: string;
  dataVersion: string;
  alertId: number;
  companyId: number;
  userId: number;
  createdAt: string;
  updatedAt: string;
};

export type AlertNotificationTransactionData = {
  type: typeof aiDisplayQueryDataType.transaction;
  items: Array<AiDisplayQueryTransactionDataItem>;
  title: string;
};

export type AlertNotificationBalanceData = {
  type: typeof aiDisplayQueryDataType.balance;
  items: Array<AiDisplayQueryBalanceDataItem>;
  title: string;
};

export type AlertNotificationInvoiceData = {
  type: typeof aiDisplayQueryDataType.invoice;
  items: Array<AiDisplayQueryInvoiceDataItem>;
  title: string;
};

export type AlertNotificationBillData = {
  type: typeof aiDisplayQueryDataType.bill;
  items: Array<AiDisplayQueryBillDataItem>;
  title: string;
};

export type AlertNotificationCustomData = {
  type: typeof aiDisplayQueryDataType.custom;
  items: Array<AiDisplayQueryCustomDataItem>;
  title: string;
};

export type AlertNotificationData =
  | AlertNotificationTransactionData
  | AlertNotificationBalanceData
  | AlertNotificationInvoiceData
  | AlertNotificationBillData
  | AlertNotificationCustomData;

export interface AlertNotificationWithData extends AlertNotification {
  type: 'alert';
  data: Array<AlertNotificationData>;
}
