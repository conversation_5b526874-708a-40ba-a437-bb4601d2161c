import { redirect } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';

import { config } from '@/config';
import { cookieKeys } from '@/constants';
import { Routes } from '@/routes';
import { checkIfFetchResponseAppUsageError } from '@/utils';

const isServer = typeof window === 'undefined';

const handleError = async (response: Response) => {
  if (!response) return;

  if (!isServer && (await checkIfFetchResponseAppUsageError(response))) {
    window.dispatchEvent(new Event('fetchUser'));
    return;
  }

  if (response.status !== 401) return;

  if (isServer) return redirect(Routes.LOGOUT);

  await fetch(Routes.LOGOUT, { credentials: 'include' });
  if (window.location.pathname !== Routes.LOGIN) window.location.replace(Routes.LOGIN);
};

const getHeaders = async (): Promise<HeadersInit> => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'X-Request-ID': uuidv4(),
  };

  if (isServer) {
    const { cookies } = await import('next/headers');
    const cookie = cookies().get(cookieKeys.authToken)?.value;
    if (cookie) headers['Cookie'] = `${cookieKeys.authToken}=${cookie}`;
  }

  return headers;
};

interface RequestOptions {
  body?: unknown;
  signal?: AbortSignal;
  parseResponse?: boolean;
  ignoreError?: boolean;
}

const request = async (
  method: 'GET' | 'POST' | 'PATCH' | 'DELETE',
  path: string,
  { body, signal, parseResponse = false, ignoreError = false }: RequestOptions,
): Promise<Response> => {
  const headers = await getHeaders();
  const res = await fetch(`${config.api.baseURL}${path}`, {
    method,
    headers,
    credentials: 'include',
    body: body ? JSON.stringify(body) : undefined,
    signal,
  });

  if (!res.ok && !ignoreError) {
    await handleError(res);
    throw res;
  }

  return parseResponse ? res.json() : res;
};

export const fetchApi = {
  get: (url: string, requestOptions: RequestOptions) => request('GET', url, requestOptions),
  post: (url: string, requestOptions: RequestOptions) => request('POST', url, requestOptions),
  patch: (url: string, requestOptions: RequestOptions) => request('PATCH', url, requestOptions),
  delete: (url: string, requestOptions: RequestOptions) => request('DELETE', url, requestOptions),
};
