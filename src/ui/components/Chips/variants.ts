import { cva } from 'class-variance-authority';

export const chipsVariants = cva('rounded-[10px] px-2 text-center uppercase body-xs-bold', {
  variants: {
    variant: {
      blue: 'bg-[#F0FDFD] hover:bg-[#E1FFFF]',
      yellow: 'bg-[#FFF9EE] hover:bg-[#FFF4DF]',
      green: 'hover:bg-[#E0FFD5]',
      purple: 'bg-[#EAECFC]',
    },
    style: {
      filled: 'border border-gray-200 py-[2px] text-gray-500 hover:border-gray-300',
      transparent: '!bg-transparent py-[2px] text-gray-500 hover:text-gray-600',
      minimal: 'py-1',
    },
  },
  compoundVariants: [
    { variant: 'purple', style: 'filled', className: 'hover:border-purple-100/30' },
    { variant: 'green', style: 'filled', className: 'bg-[#F3FFEE]' },
    { variant: 'purple', style: 'transparent', className: 'text-purple-500 hover:text-purple-700' },
    { variant: 'blue', style: 'minimal', className: 'text-turquoise-600 hover:text-turquoise-800' },
    { variant: 'yellow', style: 'minimal', className: 'text-yellow-500 hover:text-[#A86400]' },
    { variant: 'green', style: 'minimal', className: 'bg-green-100 text-[#007F46] hover:text-[#005A31]' },
    { variant: 'purple', style: 'minimal', className: 'text-purple-500 hover:bg-purple-200 hover:text-purple-700' },
  ],

  defaultVariants: {
    variant: 'blue',
    style: 'filled',
  },
});
