import { PropsWithChildren } from 'react';
import { VariantProps } from 'class-variance-authority';

import { cn } from '@/ui/utils';

import { chipsVariants } from './variants';

interface ChipsProps extends PropsWithChildren {
  style?: Exclude<VariantProps<typeof chipsVariants>['style'], null>;
  variant?: Exclude<VariantProps<typeof chipsVariants>['variant'], null>;
  className?: string;
  onClick?: () => void;
}

export default function Chips({ style, variant, children, className, onClick }: ChipsProps) {
  if (onClick) {
    return (
      <button className={cn(chipsVariants({ style, variant }), className)} onClick={onClick}>
        {children}
      </button>
    );
  }

  return <span className={cn('pointer-events-none', chipsVariants({ style, variant }), className)}>{children}</span>;
}
