import { Fragment, PropsWithChildren } from 'react';
import { VariantProps } from 'class-variance-authority';

import { Icon } from '@/ui/components';
import { cn } from '@/ui/utils';

import { Chips } from '../Chips';

import type { Tab } from './types';
import { tabsSizeVariants, tabsVariants } from './variants';

interface TabsProps<T = string> extends PropsWithChildren {
  activeTab: T;
  setActiveTab: (activeTab: T) => void;
  tabs: Tab<T>[];
  size?: VariantProps<typeof tabsSizeVariants>['size'];
  className?: string;
  itemCn?: string;
  variant?: VariantProps<typeof tabsVariants>['variant'];
}

export default function Tabs<T = string>({
  activeTab,
  setActiveTab,
  tabs,
  size = 'medium',
  variant = 'primary-filled',
  className,
  itemCn,
}: TabsProps<T>) {
  return (
    <ul className={cn('flex h-fit items-center', className)}>
      {tabs.map(({ name, value, leftIcon, rightIcon, disabled, rightBadgeText, topRightChipsText, id }, index) => {
        const isFirst = index === 0;
        const isLast = index === tabs.length - 1;
        return (
          <Fragment key={value as string}>
            <li className="flex" id={id}>
              <button
                className={cn(
                  'relative',
                  tabsSizeVariants({ size }),
                  tabsVariants({ variant, state: activeTab === value ? 'active' : 'inactive' }),
                  isFirst && 'rounded-r-none border-x',
                  isLast && 'rounded-l-none border-r',
                  !isFirst && !isLast && 'rounded-none border-r',
                  itemCn,
                )}
                onClick={() => {
                  if (activeTab !== value) setActiveTab(value);
                }}
                disabled={disabled}
              >
                {leftIcon && <Icon icon={leftIcon} cursor={disabled ? 'default' : 'pointer'} />}
                <span className="flex min-h-6 items-center">{name}</span>
                {rightIcon && !rightBadgeText && <Icon icon={rightIcon} cursor={disabled ? 'default' : 'pointer'} />}
                {rightBadgeText && (
                  <span className="flex size-6 items-center justify-center rounded-full bg-orange-500 text-white">
                    {rightBadgeText}
                  </span>
                )}
                {topRightChipsText && (
                  <Chips variant="purple" style="minimal" className="absolute right-4 top-0 -translate-y-1/2">
                    {topRightChipsText}
                  </Chips>
                )}
              </button>
            </li>
            {variant === 'primary-transparent' && index !== tabs.length - 1 && (
              <span className="h-3 w-px bg-gray-300" />
            )}
          </Fragment>
        );
      })}
    </ul>
  );
}
