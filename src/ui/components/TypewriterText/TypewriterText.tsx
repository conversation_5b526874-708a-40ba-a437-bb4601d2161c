'use client';
import { useEffect, useState } from 'react';

interface TypewriterTextProps {
  text: string;
  speed?: number;
  className?: string;
  onTextUpdate?: () => void;
}

export default function TypewriterText({ text, speed = 20, className, onTextUpdate }: TypewriterTextProps) {
  const [index, setIndex] = useState(1);

  useEffect(() => {
    if (index <= text.length) {
      const timeout = setTimeout(() => {
        setIndex((prev) => prev + 1);
        onTextUpdate?.();
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [index, text, speed, onTextUpdate]);

  return <span className={className}>{text.slice(0, index)}</span>;
}
