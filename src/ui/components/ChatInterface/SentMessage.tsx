import { cn } from '@/ui/utils';

import { MessageOptions } from './types';

interface SentMessageProps {
  message: string;
  options?: MessageOptions;
}

export default function SentMessage({ message, options }: SentMessageProps) {
  return (
    <div
      className={cn(
        'ml-auto w-fit max-w-[80%] whitespace-pre-wrap rounded-xl rounded-tr-none p-2 text-gray-800 body-m-medium',
        options?.backgroundColor ?? 'bg-turquoise-200',
      )}
    >
      {message}
    </div>
  );
}
