import { cn } from '@/ui/utils';

import { TypewriterText } from '../TypewriterText';

import { MessageOptions } from './types';

interface ReceivedMessageProps {
  message: string;
  title?: string;
  isError?: boolean;
  showAsTypewriter?: boolean;
  onTextUpdate: () => void;
  options?: MessageOptions;
}

export default function ReceivedMessage({
  message,
  title,
  isError,
  showAsTypewriter,
  onTextUpdate,
  options,
}: ReceivedMessageProps) {
  if (!isError) {
    return (
      <div className="flex w-fit max-w-[80%] flex-col gap-2">
        {title && <span className="uppercase text-gray-400 body-xs-semibold">{title}</span>}
        {showAsTypewriter ? (
          <TypewriterText
            className={cn(
              'whitespace-pre-wrap rounded-xl rounded-tl-none px-4 py-2 text-left text-gray-800 body-m-medium',
              options?.backgroundColor ?? 'bg-white',
            )}
            text={message}
            onTextUpdate={onTextUpdate}
          />
        ) : (
          <span
            className={cn(
              'whitespace-pre-wrap rounded-xl rounded-tl-none px-4 py-2 text-left text-gray-800 body-m-medium',
              options?.backgroundColor ?? 'bg-white',
            )}
          >
            {message}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="flex w-fit max-w-[80%] flex-col gap-2">
      <span className="whitespace-pre-wrap rounded-xl rounded-tl-none px-4  py-2 text-left text-gray-800 body-m-medium">
        {message}
      </span>
    </div>
  );
}
