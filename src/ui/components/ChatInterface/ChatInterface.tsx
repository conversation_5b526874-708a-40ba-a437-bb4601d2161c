'use client';
import { Fragment, useEffect, useRef } from 'react';

import { Icon, TextArea } from '@/ui/components';
import { cn } from '@/ui/utils';

import ReceivedMessage from './ReceivedMessage';
import SentMessage from './SentMessage';
import { ChatMessage, ChatMessageType, MessageOptions } from './types';

interface ChatInterfaceProps {
  messages: Array<ChatMessage>;
  onSendMessage: (message: string) => void;
  loading?: boolean;
  loadingMessage?: string;
  disabled?: boolean;
  className?: string;
  receivedMessageOptions?: MessageOptions;
  sentMessageOptions?: MessageOptions;
}

export default function ChatInterface({
  messages,
  onSendMessage,
  loading,
  loadingMessage = 'Gathering information...',
  disabled,
  className,
  receivedMessageOptions,
  sentMessageOptions,
}: ChatInterfaceProps) {
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const chatBodyRef = useRef<HTMLDivElement>(null);

  const isChatDisabled = disabled || loading;

  // Scroll to bottom at the beginning and each time when new message is added
  useEffect(() => {
    if (chatBodyRef.current) {
      chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
    }

    const observer = new MutationObserver(() => {
      if (chatBodyRef.current) {
        chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
      }
    });

    if (chatBodyRef.current) {
      observer.observe(chatBodyRef.current, { childList: true, subtree: true });
    }

    return () => observer.disconnect();
  }, []);

  const sendMessage = () => {
    if (!textareaRef.current || isChatDisabled) return;

    const preparedMessage = textareaRef.current.value.trim();
    if (!preparedMessage) return;

    onSendMessage(preparedMessage);

    textareaRef.current.value = '';
    textareaRef.current.style.height = '44px';
  };

  const onKeyEnter = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (isChatDisabled) return;

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className={cn('relative flex size-full flex-col gap-2 overflow-auto rounded-xl bg-gray-100 p-4', className)}>
      <div className="flex flex-1 flex-col gap-2 overflow-auto scroll-smooth scrollbar-hide" ref={chatBodyRef}>
        {messages.map((message, index) => (
          <Fragment key={index}>
            {message.type === ChatMessageType.SENT ? (
              <SentMessage message={message.message} options={sentMessageOptions} />
            ) : (
              <ReceivedMessage
                message={message.message}
                title={message.title}
                isError={message.isError}
                showAsTypewriter={message.showAsTypewriter}
                onTextUpdate={() => {
                  if (chatBodyRef.current) {
                    chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
                  }
                }}
                options={receivedMessageOptions}
              />
            )}
          </Fragment>
        ))}
        {loading && (
          <div className="flex items-center gap-2">
            <Icon icon="loader13" size="xs" className="animate-spin text-purple-500" />
            <span className="animate-textShine body-s-bold gradient-text">{loadingMessage}</span>
          </div>
        )}
      </div>

      <div className="relative h-auto">
        <TextArea
          onKeyDown={onKeyEnter}
          disabled={isChatDisabled}
          ref={textareaRef}
          rows={1}
          className="[&>div]:hidden [&>textarea]:h-auto [&>textarea]:max-h-[120px] [&>textarea]:min-h-[44px] [&>textarea]:pr-8 [&>textarea]:transition-all [&>textarea]:scrollbar-hide"
          onInput={() => {
            if (!textareaRef.current) return;
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
          }}
        />
        <span
          onClick={isChatDisabled ? undefined : sendMessage}
          className={cn(
            'absolute bottom-[10px] right-[10px] flex size-6 items-center justify-center rounded-full bg-gray-200 -rotate-90',
            { 'opacity-35': isChatDisabled },
          )}
        >
          <Icon cursor={isChatDisabled ? 'default' : 'pointer'} icon="arrowRight" size="s" />
        </span>
      </div>
    </div>
  );
}
